{"version": 3, "sources": ["../../@radix-ui/react-toggle/src/Toggle.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Toggle\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Toggle';\n\ntype ToggleElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface ToggleProps extends PrimitiveButtonProps {\n  /**\n   * The controlled state of the toggle.\n   */\n  pressed?: boolean;\n  /**\n   * The state of the toggle when initially rendered. Use `defaultPressed`\n   * if you do not need to control the state of the toggle.\n   * @defaultValue false\n   */\n  defaultPressed?: boolean;\n  /**\n   * The callback that fires when the state of the toggle changes.\n   */\n  onPressedChange?(pressed: boolean): void;\n}\n\nconst Toggle = React.forwardRef<ToggleElement, ToggleProps>((props, forwardedRef) => {\n  const { pressed: pressedProp, defaultPressed = false, onPressedChange, ...buttonProps } = props;\n\n  const [pressed = false, setPressed] = useControllableState({\n    prop: pressedProp,\n    onChange: onPressedChange,\n    defaultProp: defaultPressed,\n  });\n\n  return (\n    <Primitive.button\n      type=\"button\"\n      aria-pressed={pressed}\n      data-state={pressed ? 'on' : 'off'}\n      data-disabled={props.disabled ? '' : undefined}\n      {...buttonProps}\n      ref={forwardedRef}\n      onClick={composeEventHandlers(props.onClick, () => {\n        if (!props.disabled) {\n          setPressed(!pressed);\n        }\n      })}\n    />\n  );\n});\n\nToggle.displayName = NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nconst Root = Toggle;\n\nexport {\n  Toggle,\n  //\n  Root,\n};\nexport type { ToggleProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AAwCnB,yBAAA;AA/BJ,IAAM,OAAO;AAqBb,IAAM,SAAe,iBAAuC,CAAC,OAAO,iBAAiB;AACnF,QAAM,EAAE,SAAS,aAAa,iBAAiB,OAAO,iBAAiB,GAAG,YAAY,IAAI;AAE1F,QAAM,CAAC,UAAU,OAAO,UAAU,IAAI,qBAAqB;IACzD,MAAM;IACN,UAAU;IACV,aAAa;EACf,CAAC;AAED,aACE;IAAC,UAAU;IAAV;MACC,MAAK;MACL,gBAAc;MACd,cAAY,UAAU,OAAO;MAC7B,iBAAe,MAAM,WAAW,KAAK;MACpC,GAAG;MACJ,KAAK;MACL,SAAS,qBAAqB,MAAM,SAAS,MAAM;AACjD,YAAI,CAAC,MAAM,UAAU;AACnB,qBAAW,CAAC,OAAO;QACrB;MACF,CAAC;IAAA;EACH;AAEJ,CAAC;AAED,OAAO,cAAc;AAIrB,IAAM,OAAO;", "names": []}