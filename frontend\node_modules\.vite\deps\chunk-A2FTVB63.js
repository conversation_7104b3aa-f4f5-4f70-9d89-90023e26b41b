import {
  useControllableState
} from "./chunk-R2E6Y2ON.js";
import {
  composeEventHandlers
} from "./chunk-IO4MLUS7.js";
import {
  Primitive
} from "./chunk-LBQ4VWGM.js";
import {
  require_jsx_runtime
} from "./chunk-WLVB5OIP.js";
import {
  require_react
} from "./chunk-CANBAPAS.js";
import {
  __toESM
} from "./chunk-5WRI5ZAA.js";

// node_modules/@radix-ui/react-toggle/dist/index.mjs
var React = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var NAME = "Toggle";
var Toggle = React.forwardRef((props, forwardedRef) => {
  const { pressed: pressedProp, defaultPressed = false, onPressedChange, ...buttonProps } = props;
  const [pressed = false, setPressed] = useControllableState({
    prop: pressedProp,
    onChange: onPressedChange,
    defaultProp: defaultPressed
  });
  return (0, import_jsx_runtime.jsx)(
    Primitive.button,
    {
      type: "button",
      "aria-pressed": pressed,
      "data-state": pressed ? "on" : "off",
      "data-disabled": props.disabled ? "" : void 0,
      ...buttonProps,
      ref: forwardedRef,
      onClick: composeEventHandlers(props.onClick, () => {
        if (!props.disabled) {
          setPressed(!pressed);
        }
      })
    }
  );
});
Toggle.displayName = NAME;
var Root = Toggle;

export {
  Toggle,
  Root
};
//# sourceMappingURL=chunk-A2FTVB63.js.map
