import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';
export interface WalrusUploadResult {
    success: boolean;
    blobId?: string;
    error?: string;
    size?: number;
}
export interface WalrusDownloadResult {
    success: boolean;
    data?: Uint8Array;
    error?: string;
}
export interface ZkLoginTransactionParams {
    ephemeralKeyPair: {
        keypair: Ed25519Keypair;
        maxEpoch: number;
        randomness: string;
    };
    zkLoginProof: {
        proofPoints: {
            a: string[];
            b: string[][];
            c: string[];
        };
        issBase64Details: {
            value: string;
            indexMod4: number;
        };
        headerBase64: string;
    };
    jwt: string;
    userSalt: string;
}
export declare class WalrusService {
    private readonly logger;
    private walrusClient;
    private suiClient;
    private signer;
    private useUploadRelay;
    constructor();
    private initializeClients;
    uploadFile(fileData: Buffer | Uint8Array, filename: string, contentType?: string): Promise<WalrusUploadResult>;
    uploadFileWithZkLogin(fileData: Buffer | Uint8Array, filename: string, zkLoginParams: ZkLoginTransactionParams, contentType?: string): Promise<WalrusUploadResult>;
    private uploadViaRelay;
    private uploadDirect;
    private uploadDirectWithZkLogin;
    private getAddressSeed;
    private uploadFileMock;
    private generateMockBlobId;
    validateConfiguration(): {
        valid: boolean;
        errors: string[];
    };
    getConfigurationStatus(): {
        mode: 'mock' | 'upload-relay' | 'direct-upload';
        network: string;
        hasPrivateKey: boolean;
        uploadRelayUrl?: string;
    };
    getWalletInfo(): Promise<{
        address?: string;
        suiBalance?: string;
        walBalance?: string;
        error?: string;
    }>;
    downloadFile(blobId: string): Promise<WalrusDownloadResult>;
    private downloadFileMock;
    blobExists(blobId: string): Promise<boolean>;
    getBlobInfo(blobId: string): Promise<{
        exists: boolean;
        size?: number;
        error?: string;
    }>;
}
