{"version": 3, "sources": ["../../@mysten/sui/src/utils/format.ts", "../../@scure/base/index.ts", "../../@mysten/utils/src/b58.ts", "../../@mysten/utils/src/b64.ts", "../../@mysten/utils/src/hex.ts", "../../@mysten/utils/src/chunk.ts", "../../@mysten/utils/src/dataloader.ts", "../../@mysten/bcs/src/uleb.ts", "../../@mysten/bcs/src/reader.ts", "../../@mysten/bcs/src/utils.ts", "../../@mysten/bcs/src/writer.ts", "../../@mysten/bcs/src/bcs-type.ts", "../../@mysten/bcs/src/bcs.ts", "../../@mysten/bcs/src/index.ts", "../../@mysten/sui/src/utils/suins.ts", "../../@mysten/sui/src/utils/move-registry.ts", "../../@mysten/sui/src/utils/sui-types.ts", "../../@mysten/sui/src/utils/constants.ts", "../../@noble/hashes/src/crypto.ts", "../../@noble/hashes/src/utils.ts", "../../@noble/hashes/src/_blake.ts", "../../@noble/hashes/src/_md.ts", "../../@noble/hashes/src/_u64.ts", "../../@noble/hashes/src/blake2.ts", "../../@noble/hashes/src/blake2b.ts", "../../@mysten/sui/src/bcs/type-tag-serializer.ts", "../../@mysten/sui/src/bcs/bcs.ts", "../../@mysten/sui/src/bcs/effects.ts", "../../@mysten/sui/src/bcs/index.ts", "../../@mysten/sui/src/utils/dynamic-fields.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nconst ELLIPSIS = '\\u{2026}';\n\nexport function formatAddress(address: string) {\n\tif (address.length <= 6) {\n\t\treturn address;\n\t}\n\n\tconst offset = address.startsWith('0x') ? 2 : 0;\n\n\treturn `0x${address.slice(offset, offset + 4)}${ELLIPSIS}${address.slice(-4)}`;\n}\n\nexport function formatDigest(digest: string) {\n\t// Use 10 first characters\n\treturn `${digest.slice(0, 10)}${ELLIPSIS}`;\n}\n", "/*! scure-base - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n\nexport interface Coder<F, T> {\n  encode(from: F): T;\n  decode(to: T): F;\n}\n\nexport interface BytesCoder extends Coder<Uint8Array, string> {\n  encode: (data: Uint8Array) => string;\n  decode: (str: string) => Uint8Array;\n}\n\nfunction isBytes(a: unknown): a is Uint8Array {\n  return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b: Uint8Array | undefined, ...lengths: number[]): void {\n  if (!isBytes(b)) throw new Error('Uint8Array expected');\n  if (lengths.length > 0 && !lengths.includes(b.length))\n    throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n\nfunction isArrayOf(isString: boolean, arr: any[]) {\n  if (!Array.isArray(arr)) return false;\n  if (arr.length === 0) return true;\n  if (isString) {\n    return arr.every((item) => typeof item === 'string');\n  } else {\n    return arr.every((item) => Number.isSafeInteger(item));\n  }\n}\n\n// no abytes: seems to have 10% slowdown. Why?!\n\nfunction afn(input: Function): input is Function {\n  if (typeof input !== 'function') throw new Error('function expected');\n  return true;\n}\n\nfunction astr(label: string, input: unknown): input is string {\n  if (typeof input !== 'string') throw new Error(`${label}: string expected`);\n  return true;\n}\n\nfunction anumber(n: number): void {\n  if (!Number.isSafeInteger(n)) throw new Error(`invalid integer: ${n}`);\n}\n\nfunction aArr(input: any[]) {\n  if (!Array.isArray(input)) throw new Error('array expected');\n}\nfunction astrArr(label: string, input: string[]) {\n  if (!isArrayOf(true, input)) throw new Error(`${label}: array of strings expected`);\n}\nfunction anumArr(label: string, input: number[]) {\n  if (!isArrayOf(false, input)) throw new Error(`${label}: array of numbers expected`);\n}\n\n// TODO: some recusive type inference so it would check correct order of input/output inside rest?\n// like <string, number>, <number, bytes>, <bytes, float>\ntype Chain = [Coder<any, any>, ...Coder<any, any>[]];\n// Extract info from Coder type\ntype Input<F> = F extends Coder<infer T, any> ? T : never;\ntype Output<F> = F extends Coder<any, infer T> ? T : never;\n// Generic function for arrays\ntype First<T> = T extends [infer U, ...any[]] ? U : never;\ntype Last<T> = T extends [...any[], infer U] ? U : never;\ntype Tail<T> = T extends [any, ...infer U] ? U : never;\n\ntype AsChain<C extends Chain, Rest = Tail<C>> = {\n  // C[K] = Coder<Input<C[K]>, Input<Rest[k]>>\n  [K in keyof C]: Coder<Input<C[K]>, Input<K extends keyof Rest ? Rest[K] : any>>;\n};\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction chain<T extends Chain & AsChain<T>>(...args: T): Coder<Input<First<T>>, Output<Last<T>>> {\n  const id = (a: any) => a;\n  // Wrap call in closure so JIT can inline calls\n  const wrap = (a: any, b: any) => (c: any) => a(b(c));\n  // Construct chain of args[-1].encode(args[-2].encode([...]))\n  const encode = args.map((x) => x.encode).reduceRight(wrap, id);\n  // Construct chain of args[0].decode(args[1].decode(...))\n  const decode = args.map((x) => x.decode).reduce(wrap, id);\n  return { encode, decode };\n}\n\n/**\n * Encodes integer radix representation to array of strings using alphabet and back.\n * Could also be array of strings.\n * @__NO_SIDE_EFFECTS__\n */\nfunction alphabet(letters: string | string[]): Coder<number[], string[]> {\n  // mapping 1 to \"b\"\n  const lettersA = typeof letters === 'string' ? letters.split('') : letters;\n  const len = lettersA.length;\n  astrArr('alphabet', lettersA);\n\n  // mapping \"b\" to 1\n  const indexes = new Map(lettersA.map((l, i) => [l, i]));\n  return {\n    encode: (digits: number[]) => {\n      aArr(digits);\n      return digits.map((i) => {\n        if (!Number.isSafeInteger(i) || i < 0 || i >= len)\n          throw new Error(\n            `alphabet.encode: digit index outside alphabet \"${i}\". Allowed: ${letters}`\n          );\n        return lettersA[i]!;\n      });\n    },\n    decode: (input: string[]): number[] => {\n      aArr(input);\n      return input.map((letter) => {\n        astr('alphabet.decode', letter);\n        const i = indexes.get(letter);\n        if (i === undefined) throw new Error(`Unknown letter: \"${letter}\". Allowed: ${letters}`);\n        return i;\n      });\n    },\n  };\n}\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction join(separator = ''): Coder<string[], string> {\n  astr('join', separator);\n  return {\n    encode: (from) => {\n      astrArr('join.decode', from);\n      return from.join(separator);\n    },\n    decode: (to) => {\n      astr('join.decode', to);\n      return to.split(separator);\n    },\n  };\n}\n\n/**\n * Pad strings array so it has integer number of bits\n * @__NO_SIDE_EFFECTS__\n */\nfunction padding(bits: number, chr = '='): Coder<string[], string[]> {\n  anumber(bits);\n  astr('padding', chr);\n  return {\n    encode(data: string[]): string[] {\n      astrArr('padding.encode', data);\n      while ((data.length * bits) % 8) data.push(chr);\n      return data;\n    },\n    decode(input: string[]): string[] {\n      astrArr('padding.decode', input);\n      let end = input.length;\n      if ((end * bits) % 8)\n        throw new Error('padding: invalid, string should have whole number of bytes');\n      for (; end > 0 && input[end - 1] === chr; end--) {\n        const last = end - 1;\n        const byte = last * bits;\n        if (byte % 8 === 0) throw new Error('padding: invalid, string has too much padding');\n      }\n      return input.slice(0, end);\n    },\n  };\n}\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction normalize<T>(fn: (val: T) => T): Coder<T, T> {\n  afn(fn);\n  return { encode: (from: T) => from, decode: (to: T) => fn(to) };\n}\n\n/**\n * Slow: O(n^2) time complexity\n */\nfunction convertRadix(data: number[], from: number, to: number): number[] {\n  // base 1 is impossible\n  if (from < 2) throw new Error(`convertRadix: invalid from=${from}, base cannot be less than 2`);\n  if (to < 2) throw new Error(`convertRadix: invalid to=${to}, base cannot be less than 2`);\n  aArr(data);\n  if (!data.length) return [];\n  let pos = 0;\n  const res = [];\n  const digits = Array.from(data, (d) => {\n    anumber(d);\n    if (d < 0 || d >= from) throw new Error(`invalid integer: ${d}`);\n    return d;\n  });\n  const dlen = digits.length;\n  while (true) {\n    let carry = 0;\n    let done = true;\n    for (let i = pos; i < dlen; i++) {\n      const digit = digits[i]!;\n      const fromCarry = from * carry;\n      const digitBase = fromCarry + digit;\n      if (\n        !Number.isSafeInteger(digitBase) ||\n        fromCarry / from !== carry ||\n        digitBase - digit !== fromCarry\n      ) {\n        throw new Error('convertRadix: carry overflow');\n      }\n      const div = digitBase / to;\n      carry = digitBase % to;\n      const rounded = Math.floor(div);\n      digits[i] = rounded;\n      if (!Number.isSafeInteger(rounded) || rounded * to + carry !== digitBase)\n        throw new Error('convertRadix: carry overflow');\n      if (!done) continue;\n      else if (!rounded) pos = i;\n      else done = false;\n    }\n    res.push(carry);\n    if (done) break;\n  }\n  for (let i = 0; i < data.length - 1 && data[i] === 0; i++) res.push(0);\n  return res.reverse();\n}\n\nconst gcd = (a: number, b: number): number => (b === 0 ? a : gcd(b, a % b));\nconst radix2carry = /* @__NO_SIDE_EFFECTS__ */ (from: number, to: number) =>\n  from + (to - gcd(from, to));\nconst powers: number[] = /* @__PURE__ */ (() => {\n  let res = [];\n  for (let i = 0; i < 40; i++) res.push(2 ** i);\n  return res;\n})();\n/**\n * Implemented with numbers, because BigInt is 5x slower\n */\nfunction convertRadix2(data: number[], from: number, to: number, padding: boolean): number[] {\n  aArr(data);\n  if (from <= 0 || from > 32) throw new Error(`convertRadix2: wrong from=${from}`);\n  if (to <= 0 || to > 32) throw new Error(`convertRadix2: wrong to=${to}`);\n  if (radix2carry(from, to) > 32) {\n    throw new Error(\n      `convertRadix2: carry overflow from=${from} to=${to} carryBits=${radix2carry(from, to)}`\n    );\n  }\n  let carry = 0;\n  let pos = 0; // bitwise position in current element\n  const max = powers[from]!;\n  const mask = powers[to]! - 1;\n  const res: number[] = [];\n  for (const n of data) {\n    anumber(n);\n    if (n >= max) throw new Error(`convertRadix2: invalid data word=${n} from=${from}`);\n    carry = (carry << from) | n;\n    if (pos + from > 32) throw new Error(`convertRadix2: carry overflow pos=${pos} from=${from}`);\n    pos += from;\n    for (; pos >= to; pos -= to) res.push(((carry >> (pos - to)) & mask) >>> 0);\n    const pow = powers[pos];\n    if (pow === undefined) throw new Error('invalid carry');\n    carry &= pow - 1; // clean carry, otherwise it will cause overflow\n  }\n  carry = (carry << (to - pos)) & mask;\n  if (!padding && pos >= from) throw new Error('Excess padding');\n  if (!padding && carry > 0) throw new Error(`Non-zero padding: ${carry}`);\n  if (padding && pos > 0) res.push(carry >>> 0);\n  return res;\n}\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix(num: number): Coder<Uint8Array, number[]> {\n  anumber(num);\n  const _256 = 2 ** 8;\n  return {\n    encode: (bytes: Uint8Array) => {\n      if (!isBytes(bytes)) throw new Error('radix.encode input should be Uint8Array');\n      return convertRadix(Array.from(bytes), _256, num);\n    },\n    decode: (digits: number[]) => {\n      anumArr('radix.decode', digits);\n      return Uint8Array.from(convertRadix(digits, num, _256));\n    },\n  };\n}\n\n/**\n * If both bases are power of same number (like `2**8 <-> 2**64`),\n * there is a linear algorithm. For now we have implementation for power-of-two bases only.\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix2(bits: number, revPadding = false): Coder<Uint8Array, number[]> {\n  anumber(bits);\n  if (bits <= 0 || bits > 32) throw new Error('radix2: bits should be in (0..32]');\n  if (radix2carry(8, bits) > 32 || radix2carry(bits, 8) > 32)\n    throw new Error('radix2: carry overflow');\n  return {\n    encode: (bytes: Uint8Array) => {\n      if (!isBytes(bytes)) throw new Error('radix2.encode input should be Uint8Array');\n      return convertRadix2(Array.from(bytes), 8, bits, !revPadding);\n    },\n    decode: (digits: number[]) => {\n      anumArr('radix2.decode', digits);\n      return Uint8Array.from(convertRadix2(digits, bits, 8, revPadding));\n    },\n  };\n}\n\ntype ArgumentTypes<F extends Function> = F extends (...args: infer A) => any ? A : never;\nfunction unsafeWrapper<T extends (...args: any) => any>(fn: T) {\n  afn(fn);\n  return function (...args: ArgumentTypes<T>): ReturnType<T> | void {\n    try {\n      return fn.apply(null, args);\n    } catch (e) {}\n  };\n}\n\nfunction checksum(\n  len: number,\n  fn: (data: Uint8Array) => Uint8Array\n): Coder<Uint8Array, Uint8Array> {\n  anumber(len);\n  afn(fn);\n  return {\n    encode(data: Uint8Array) {\n      if (!isBytes(data)) throw new Error('checksum.encode: input should be Uint8Array');\n      const sum = fn(data).slice(0, len);\n      const res = new Uint8Array(data.length + len);\n      res.set(data);\n      res.set(sum, data.length);\n      return res;\n    },\n    decode(data: Uint8Array) {\n      if (!isBytes(data)) throw new Error('checksum.decode: input should be Uint8Array');\n      const payload = data.slice(0, -len);\n      const oldChecksum = data.slice(-len);\n      const newChecksum = fn(payload).slice(0, len);\n      for (let i = 0; i < len; i++)\n        if (newChecksum[i] !== oldChecksum[i]) throw new Error('Invalid checksum');\n      return payload;\n    },\n  };\n}\n\n// prettier-ignore\nexport const utils: { alphabet: typeof alphabet; chain: typeof chain; checksum: typeof checksum; convertRadix: typeof convertRadix; convertRadix2: typeof convertRadix2; radix: typeof radix; radix2: typeof radix2; join: typeof join; padding: typeof padding; } = {\n  alphabet, chain, checksum, convertRadix, convertRadix2, radix, radix2, join, padding,\n};\n\n// RFC 4648 aka RFC 3548\n// ---------------------\n\n/**\n * base16 encoding from RFC 4648.\n * @example\n * ```js\n * base16.encode(Uint8Array.from([0x12, 0xab]));\n * // => '12AB'\n * ```\n */\nexport const base16: BytesCoder = chain(radix2(4), alphabet('0123456789ABCDEF'), join(''));\n\n/**\n * base32 encoding from RFC 4648. Has padding.\n * Use `base32nopad` for unpadded version.\n * Also check out `base32hex`, `base32hexnopad`, `base32crockford`.\n * @example\n * ```js\n * base32.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'CKVQ===='\n * base32.decode('CKVQ====');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32: BytesCoder = chain(\n  radix2(5),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'),\n  padding(5),\n  join('')\n);\n\n/**\n * base32 encoding from RFC 4648. No padding.\n * Use `base32` for padded version.\n * Also check out `base32hex`, `base32hexnopad`, `base32crockford`.\n * @example\n * ```js\n * base32nopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'CKVQ'\n * base32nopad.decode('CKVQ');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32nopad: BytesCoder = chain(\n  radix2(5),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'),\n  join('')\n);\n/**\n * base32 encoding from RFC 4648. Padded. Compared to ordinary `base32`, slightly different alphabet.\n * Use `base32hexnopad` for unpadded version.\n * @example\n * ```js\n * base32hex.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ALG===='\n * base32hex.decode('2ALG====');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32hex: BytesCoder = chain(\n  radix2(5),\n  alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'),\n  padding(5),\n  join('')\n);\n\n/**\n * base32 encoding from RFC 4648. No padding. Compared to ordinary `base32`, slightly different alphabet.\n * Use `base32hex` for padded version.\n * @example\n * ```js\n * base32hexnopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ALG'\n * base32hexnopad.decode('2ALG');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32hexnopad: BytesCoder = chain(\n  radix2(5),\n  alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'),\n  join('')\n);\n/**\n * base32 encoding from RFC 4648. Doug Crockford's version.\n * https://www.crockford.com/base32.html\n * @example\n * ```js\n * base32crockford.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ANG'\n * base32crockford.decode('2ANG');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32crockford: BytesCoder = chain(\n  radix2(5),\n  alphabet('0123456789ABCDEFGHJKMNPQRSTVWXYZ'),\n  join(''),\n  normalize((s: string) => s.toUpperCase().replace(/O/g, '0').replace(/[IL]/g, '1'))\n);\n\n// Built-in base64 conversion https://caniuse.com/mdn-javascript_builtins_uint8array_frombase64\n// prettier-ignore\nconst hasBase64Builtin: boolean = /* @__PURE__ */ (() =>\n  typeof (Uint8Array as any).from([]).toBase64 === 'function' &&\n  typeof (Uint8Array as any).fromBase64 === 'function')();\n\nconst decodeBase64Builtin = (s: string, isUrl: boolean) => {\n  astr('base64', s);\n  const re = isUrl ? /^[A-Za-z0-9=_-]+$/ : /^[A-Za-z0-9=+/]+$/;\n  const alphabet = isUrl ? 'base64url' : 'base64';\n  if (s.length > 0 && !re.test(s)) throw new Error('invalid base64');\n  return (Uint8Array as any).fromBase64(s, { alphabet, lastChunkHandling: 'strict' });\n};\n\n/**\n * base64 from RFC 4648. Padded.\n * Use `base64nopad` for unpadded version.\n * Also check out `base64url`, `base64urlnopad`.\n * Falls back to built-in function, when available.\n * @example\n * ```js\n * base64.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs='\n * base64.decode('Eqs=');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\n// prettier-ignore\nexport const base64: BytesCoder = hasBase64Builtin ? {\n  encode(b) { abytes(b); return (b as any).toBase64(); },\n  decode(s) { return decodeBase64Builtin(s, false); },\n} : chain(\n  radix2(6),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'),\n  padding(6),\n  join('')\n);\n/**\n * base64 from RFC 4648. No padding.\n * Use `base64` for padded version.\n * @example\n * ```js\n * base64nopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs'\n * base64nopad.decode('Eqs');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base64nopad: BytesCoder = chain(\n  radix2(6),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'),\n  join('')\n);\n\n/**\n * base64 from RFC 4648, using URL-safe alphabet. Padded.\n * Use `base64urlnopad` for unpadded version.\n * Falls back to built-in function, when available.\n * @example\n * ```js\n * base64url.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs='\n * base64url.decode('Eqs=');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\n// prettier-ignore\nexport const base64url: BytesCoder = hasBase64Builtin ? {\n  encode(b) { abytes(b); return (b as any).toBase64({ alphabet: 'base64url' }); },\n  decode(s) { return decodeBase64Builtin(s, true); },\n} : chain(\n  radix2(6),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'),\n  padding(6),\n  join('')\n);\n\n/**\n * base64 from RFC 4648, using URL-safe alphabet. No padding.\n * Use `base64url` for padded version.\n * @example\n * ```js\n * base64urlnopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs'\n * base64urlnopad.decode('Eqs');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base64urlnopad: BytesCoder = chain(\n  radix2(6),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'),\n  join('')\n);\n\n// base58 code\n// -----------\nconst genBase58 = /* @__NO_SIDE_EFFECTS__ */ (abc: string) =>\n  chain(radix(58), alphabet(abc), join(''));\n\n/**\n * base58: base64 without ambigous characters +, /, 0, O, I, l.\n * Quadratic (O(n^2)) - so, can't be used on large inputs.\n * @example\n * ```js\n * base58.decode('01abcdef');\n * // => '3UhJW'\n * ```\n */\nexport const base58: BytesCoder = genBase58(\n  '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz'\n);\n/**\n * base58: flickr version. Check out `base58`.\n */\nexport const base58flickr: BytesCoder = genBase58(\n  '123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ'\n);\n/**\n * base58: XRP version. Check out `base58`.\n */\nexport const base58xrp: BytesCoder = genBase58(\n  'rpshnaf39wBUDNEGHJKLM4PQRST7VWXYZ2bcdeCg65jkm8oFqi1tuvAxyz'\n);\n\n// Data len (index) -> encoded block len\nconst XMR_BLOCK_LEN = [0, 2, 3, 5, 6, 7, 9, 10, 11];\n\n/**\n * base58: XMR version. Check out `base58`.\n * Done in 8-byte blocks (which equals 11 chars in decoding). Last (non-full) block padded with '1' to size in XMR_BLOCK_LEN.\n * Block encoding significantly reduces quadratic complexity of base58.\n */\nexport const base58xmr: BytesCoder = {\n  encode(data: Uint8Array) {\n    let res = '';\n    for (let i = 0; i < data.length; i += 8) {\n      const block = data.subarray(i, i + 8);\n      res += base58.encode(block).padStart(XMR_BLOCK_LEN[block.length]!, '1');\n    }\n    return res;\n  },\n  decode(str: string) {\n    let res: number[] = [];\n    for (let i = 0; i < str.length; i += 11) {\n      const slice = str.slice(i, i + 11);\n      const blockLen = XMR_BLOCK_LEN.indexOf(slice.length);\n      const block = base58.decode(slice);\n      for (let j = 0; j < block.length - blockLen; j++) {\n        if (block[j] !== 0) throw new Error('base58xmr: wrong padding');\n      }\n      res = res.concat(Array.from(block.slice(block.length - blockLen)));\n    }\n    return Uint8Array.from(res);\n  },\n};\n\n/**\n * Method, which creates base58check encoder.\n * Requires function, calculating sha256.\n */\nexport const createBase58check = (sha256: (data: Uint8Array) => Uint8Array): BytesCoder =>\n  chain(\n    checksum(4, (data) => sha256(sha256(data))),\n    base58\n  );\n\n/**\n * Use `createBase58check` instead.\n * @deprecated\n */\nexport const base58check: (sha256: (data: Uint8Array) => Uint8Array) => BytesCoder =\n  createBase58check;\n\n// Bech32 code\n// -----------\nexport interface Bech32Decoded<Prefix extends string = string> {\n  prefix: Prefix;\n  words: number[];\n}\nexport interface Bech32DecodedWithArray<Prefix extends string = string> {\n  prefix: Prefix;\n  words: number[];\n  bytes: Uint8Array;\n}\n\nconst BECH_ALPHABET: Coder<number[], string> = chain(\n  alphabet('qpzry9x8gf2tvdw0s3jn54khce6mua7l'),\n  join('')\n);\n\nconst POLYMOD_GENERATORS = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3];\nfunction bech32Polymod(pre: number): number {\n  const b = pre >> 25;\n  let chk = (pre & 0x1ffffff) << 5;\n  for (let i = 0; i < POLYMOD_GENERATORS.length; i++) {\n    if (((b >> i) & 1) === 1) chk ^= POLYMOD_GENERATORS[i]!;\n  }\n  return chk;\n}\n\nfunction bechChecksum(prefix: string, words: number[], encodingConst = 1): string {\n  const len = prefix.length;\n  let chk = 1;\n  for (let i = 0; i < len; i++) {\n    const c = prefix.charCodeAt(i);\n    if (c < 33 || c > 126) throw new Error(`Invalid prefix (${prefix})`);\n    chk = bech32Polymod(chk) ^ (c >> 5);\n  }\n  chk = bech32Polymod(chk);\n  for (let i = 0; i < len; i++) chk = bech32Polymod(chk) ^ (prefix.charCodeAt(i) & 0x1f);\n  for (let v of words) chk = bech32Polymod(chk) ^ v;\n  for (let i = 0; i < 6; i++) chk = bech32Polymod(chk);\n  chk ^= encodingConst;\n  return BECH_ALPHABET.encode(convertRadix2([chk % powers[30]!], 30, 5, false));\n}\n\nexport interface Bech32 {\n  encode<Prefix extends string>(\n    prefix: Prefix,\n    words: number[] | Uint8Array,\n    limit?: number | false\n  ): `${Lowercase<Prefix>}1${string}`;\n  decode<Prefix extends string>(\n    str: `${Prefix}1${string}`,\n    limit?: number | false\n  ): Bech32Decoded<Prefix>;\n  encodeFromBytes(prefix: string, bytes: Uint8Array): string;\n  decodeToBytes(str: string): Bech32DecodedWithArray;\n  decodeUnsafe(str: string, limit?: number | false): void | Bech32Decoded<string>;\n  fromWords(to: number[]): Uint8Array;\n  fromWordsUnsafe(to: number[]): void | Uint8Array;\n  toWords(from: Uint8Array): number[];\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction genBech32(encoding: 'bech32' | 'bech32m'): Bech32 {\n  const ENCODING_CONST = encoding === 'bech32' ? 1 : 0x2bc830a3;\n  const _words = radix2(5);\n  const fromWords = _words.decode;\n  const toWords = _words.encode;\n  const fromWordsUnsafe = unsafeWrapper(fromWords);\n\n  function encode<Prefix extends string>(\n    prefix: Prefix,\n    words: number[] | Uint8Array,\n    limit: number | false = 90\n  ): `${Lowercase<Prefix>}1${string}` {\n    astr('bech32.encode prefix', prefix);\n    if (isBytes(words)) words = Array.from(words);\n    anumArr('bech32.encode', words);\n    const plen = prefix.length;\n    if (plen === 0) throw new TypeError(`Invalid prefix length ${plen}`);\n    const actualLength = plen + 7 + words.length;\n    if (limit !== false && actualLength > limit)\n      throw new TypeError(`Length ${actualLength} exceeds limit ${limit}`);\n    const lowered = prefix.toLowerCase();\n    const sum = bechChecksum(lowered, words, ENCODING_CONST);\n    return `${lowered}1${BECH_ALPHABET.encode(words)}${sum}` as `${Lowercase<Prefix>}1${string}`;\n  }\n\n  function decode<Prefix extends string>(\n    str: `${Prefix}1${string}`,\n    limit?: number | false\n  ): Bech32Decoded<Prefix>;\n  function decode(str: string, limit?: number | false): Bech32Decoded;\n  function decode(str: string, limit: number | false = 90): Bech32Decoded {\n    astr('bech32.decode input', str);\n    const slen = str.length;\n    if (slen < 8 || (limit !== false && slen > limit))\n      throw new TypeError(`invalid string length: ${slen} (${str}). Expected (8..${limit})`);\n    // don't allow mixed case\n    const lowered = str.toLowerCase();\n    if (str !== lowered && str !== str.toUpperCase())\n      throw new Error(`String must be lowercase or uppercase`);\n    const sepIndex = lowered.lastIndexOf('1');\n    if (sepIndex === 0 || sepIndex === -1)\n      throw new Error(`Letter \"1\" must be present between prefix and data only`);\n    const prefix = lowered.slice(0, sepIndex);\n    const data = lowered.slice(sepIndex + 1);\n    if (data.length < 6) throw new Error('Data must be at least 6 characters long');\n    const words = BECH_ALPHABET.decode(data).slice(0, -6);\n    const sum = bechChecksum(prefix, words, ENCODING_CONST);\n    if (!data.endsWith(sum)) throw new Error(`Invalid checksum in ${str}: expected \"${sum}\"`);\n    return { prefix, words };\n  }\n\n  const decodeUnsafe = unsafeWrapper(decode);\n\n  function decodeToBytes(str: string): Bech32DecodedWithArray {\n    const { prefix, words } = decode(str, false);\n    return { prefix, words, bytes: fromWords(words) };\n  }\n\n  function encodeFromBytes(prefix: string, bytes: Uint8Array) {\n    return encode(prefix, toWords(bytes));\n  }\n\n  return {\n    encode,\n    decode,\n    encodeFromBytes,\n    decodeToBytes,\n    decodeUnsafe,\n    fromWords,\n    fromWordsUnsafe,\n    toWords,\n  };\n}\n\n/**\n * bech32 from BIP 173. Operates on words.\n * For high-level, check out scure-btc-signer:\n * https://github.com/paulmillr/scure-btc-signer.\n */\nexport const bech32: Bech32 = genBech32('bech32');\n\n/**\n * bech32m from BIP 350. Operates on words.\n * It was to mitigate `bech32` weaknesses.\n * For high-level, check out scure-btc-signer:\n * https://github.com/paulmillr/scure-btc-signer.\n */\nexport const bech32m: Bech32 = genBech32('bech32m');\n\ndeclare const TextEncoder: any;\ndeclare const TextDecoder: any;\n\n/**\n * UTF-8-to-byte decoder. Uses built-in TextDecoder / TextEncoder.\n * @example\n * ```js\n * const b = utf8.decode(\"hey\"); // => new Uint8Array([ 104, 101, 121 ])\n * const str = utf8.encode(b); // \"hey\"\n * ```\n */\nexport const utf8: BytesCoder = {\n  encode: (data) => new TextDecoder().decode(data),\n  decode: (str) => new TextEncoder().encode(str),\n};\n\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\n// prettier-ignore\nconst hasHexBuiltin: boolean = /* @__PURE__ */ (() =>\n  typeof (Uint8Array as any).from([]).toHex === 'function' &&\n  typeof (Uint8Array as any).fromHex === 'function')();\n// prettier-ignore\nconst hexBuiltin: BytesCoder = {\n  encode(data) { abytes(data); return (data as any).toHex(); },\n  decode(s) { astr('hex', s); return (Uint8Array as any).fromHex(s); },\n};\n/**\n * hex string decoder. Uses built-in function, when available.\n * @example\n * ```js\n * const b = hex.decode(\"0102ff\"); // => new Uint8Array([ 1, 2, 255 ])\n * const str = hex.encode(b); // \"0102ff\"\n * ```\n */\nexport const hex: BytesCoder = hasHexBuiltin\n  ? hexBuiltin\n  : chain(\n      radix2(4),\n      alphabet('0123456789abcdef'),\n      join(''),\n      normalize((s: string) => {\n        if (typeof s !== 'string' || s.length % 2 !== 0)\n          throw new TypeError(\n            `hex.decode: expected string, got ${typeof s} with length ${s.length}`\n          );\n        return s.toLowerCase();\n      })\n    );\n\nexport type SomeCoders = {\n  utf8: BytesCoder;\n  hex: BytesCoder;\n  base16: BytesCoder;\n  base32: BytesCoder;\n  base64: BytesCoder;\n  base64url: BytesCoder;\n  base58: BytesCoder;\n  base58xmr: BytesCoder;\n};\n// prettier-ignore\nconst CODERS: SomeCoders = {\n  utf8, hex, base16, base32, base64, base64url, base58, base58xmr\n};\ntype CoderType = keyof SomeCoders;\nconst coderTypeError =\n  'Invalid encoding type. Available types: utf8, hex, base16, base32, base64, base64url, base58, base58xmr';\n\n/** @deprecated */\nexport const bytesToString = (type: CoderType, bytes: Uint8Array): string => {\n  if (typeof type !== 'string' || !CODERS.hasOwnProperty(type)) throw new TypeError(coderTypeError);\n  if (!isBytes(bytes)) throw new TypeError('bytesToString() expects Uint8Array');\n  return CODERS[type].encode(bytes);\n};\n\n/** @deprecated */\nexport const str: (type: CoderType, bytes: Uint8Array) => string = bytesToString; // as in python, but for bytes only\n\n/** @deprecated */\nexport const stringToBytes = (type: CoderType, str: string): Uint8Array => {\n  if (!CODERS.hasOwnProperty(type)) throw new TypeError(coderTypeError);\n  if (typeof str !== 'string') throw new TypeError('stringToBytes() expects string');\n  return CODERS[type].decode(str);\n};\n/** @deprecated */\nexport const bytes: (type: CoderType, str: string) => Uint8Array = stringToBytes;\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { base58 } from '@scure/base';\n\nexport const toBase58 = (buffer: Uint8Array) => base58.encode(buffer);\nexport const fromBase58 = (str: string) => base58.decode(str);\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport function fromBase64(base64String: string): Uint8Array {\n\treturn Uint8Array.from(atob(base64String), (char) => char.charCodeAt(0));\n}\n\nconst CHUNK_SIZE = 8192;\nexport function toBase64(bytes: Uint8Array): string {\n\t// Special-case the simple case for speed's sake.\n\tif (bytes.length < CHUNK_SIZE) {\n\t\treturn btoa(String.fromCharCode(...bytes));\n\t}\n\n\tlet output = '';\n\tfor (var i = 0; i < bytes.length; i += CHUNK_SIZE) {\n\t\tconst chunk = bytes.slice(i, i + CHUNK_SIZE);\n\t\toutput += String.fromCharCode(...chunk);\n\t}\n\n\treturn btoa(output);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport function fromHex(hexStr: string): Uint8Array {\n\tconst normalized = hexStr.startsWith('0x') ? hexStr.slice(2) : hexStr;\n\tconst padded = normalized.length % 2 === 0 ? normalized : `0${normalized}`;\n\tconst intArr = padded.match(/[0-9a-fA-F]{2}/g)?.map((byte) => parseInt(byte, 16)) ?? [];\n\n\tif (intArr.length !== padded.length / 2) {\n\t\tthrow new Error(`Invalid hex string ${hexStr}`);\n\t}\n\n\treturn Uint8Array.from(intArr);\n}\n\nexport function toHex(bytes: Uint8Array): string {\n\treturn bytes.reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport function chunk<T>(array: readonly T[], size: number): T[][] {\n\treturn Array.from({ length: Math.ceil(array.length / size) }, (_, i) => {\n\t\treturn array.slice(i * size, (i + 1) * size);\n\t});\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\n/** Copied from https://github.com/graphql/dataloader/blob/a10773043d41a56bde4219c155fcf5633e6c9bcb/src/index.js */\n\n/**\n * A `DataLoader` creates a public API for loading data from a particular\n * data back-end with unique keys such as the `id` column of a SQL table or\n * document name in a MongoDB database, given a batch loading function.\n *\n * Each `DataLoader` instance contains a unique memoized cache. Use caution when\n * used in long-lived applications or those which serve many users with\n * different access permissions and consider creating a new instance per\n * web request.\n */\nexport class DataLoader<K, V, C = K> {\n\tconstructor(batchLoadFn: DataLoader.BatchLoadFn<K, V>, options?: DataLoader.Options<K, V, C>) {\n\t\tif (typeof batchLoadFn !== 'function') {\n\t\t\tthrow new TypeError(\n\t\t\t\t'DataLoader must be constructed with a function which accepts ' +\n\t\t\t\t\t`Array<key> and returns Promise<Array<value>>, but got: ${batchLoadFn}.`,\n\t\t\t);\n\t\t}\n\t\tthis._batchLoadFn = batchLoadFn;\n\t\tthis._maxBatchSize = getValidMaxBatchSize(options);\n\t\tthis._batchScheduleFn = getValidBatchScheduleFn(options);\n\t\tthis._cacheKeyFn = getValidCacheKeyFn(options);\n\t\tthis._cacheMap = getValidCacheMap(options);\n\t\tthis._batch = null;\n\t\tthis.name = getValidName(options);\n\t}\n\n\t// Private\n\t_batchLoadFn: DataLoader.BatchLoadFn<K, V>;\n\t_maxBatchSize: number;\n\t_batchScheduleFn: (cb: () => void) => void;\n\t_cacheKeyFn: (key: K) => C;\n\t_cacheMap: DataLoader.CacheMap<C, Promise<V>> | null;\n\t_batch: Batch<K, V> | null;\n\n\t/**\n\t * Loads a key, returning a `Promise` for the value represented by that key.\n\t */\n\tload(key: K): Promise<V> {\n\t\tif (key === null || key === undefined) {\n\t\t\tthrow new TypeError(\n\t\t\t\t`The loader.load() function must be called with a value, but got: ${String(key)}.`,\n\t\t\t);\n\t\t}\n\n\t\tconst batch = getCurrentBatch(this);\n\t\tconst cacheMap = this._cacheMap;\n\t\tlet cacheKey: C;\n\n\t\t// If caching and there is a cache-hit, return cached Promise.\n\t\tif (cacheMap) {\n\t\t\tcacheKey = this._cacheKeyFn(key);\n\t\t\tconst cachedPromise = cacheMap.get(cacheKey);\n\t\t\tif (cachedPromise) {\n\t\t\t\tconst cacheHits = batch.cacheHits || (batch.cacheHits = []);\n\t\t\t\treturn new Promise((resolve) => {\n\t\t\t\t\tcacheHits.push(() => {\n\t\t\t\t\t\tresolve(cachedPromise);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\t// Otherwise, produce a new Promise for this key, and enqueue it to be\n\t\t// dispatched along with the current batch.\n\t\tbatch.keys.push(key);\n\t\tconst promise = new Promise<V>((resolve, reject) => {\n\t\t\tbatch.callbacks.push({ resolve, reject });\n\t\t});\n\n\t\t// If caching, cache this promise.\n\t\tif (cacheMap) {\n\t\t\tcacheMap.set(cacheKey!, promise);\n\t\t}\n\n\t\treturn promise;\n\t}\n\n\t/**\n\t * Loads multiple keys, promising an array of values:\n\t *\n\t *     var [ a, b ] = await myLoader.loadMany([ 'a', 'b' ]);\n\t *\n\t * This is similar to the more verbose:\n\t *\n\t *     var [ a, b ] = await Promise.all([\n\t *       myLoader.load('a'),\n\t *       myLoader.load('b')\n\t *     ]);\n\t *\n\t * However it is different in the case where any load fails. Where\n\t * Promise.all() would reject, loadMany() always resolves, however each result\n\t * is either a value or an Error instance.\n\t *\n\t *     var [ a, b, c ] = await myLoader.loadMany([ 'a', 'b', 'badkey' ]);\n\t *     // c instanceof Error\n\t *\n\t */\n\tloadMany(keys: ReadonlyArray<K>): Promise<Array<V | Error>> {\n\t\tif (!isArrayLike(keys)) {\n\t\t\tthrow new TypeError(\n\t\t\t\t`The loader.loadMany() function must be called with Array<key>, but got: ${keys}.`,\n\t\t\t);\n\t\t}\n\t\t// Support ArrayLike by using only minimal property access\n\t\tconst loadPromises = [];\n\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\tloadPromises.push(this.load(keys[i]).catch((error) => error));\n\t\t}\n\t\treturn Promise.all(loadPromises);\n\t}\n\n\t/**\n\t * Clears the value at `key` from the cache, if it exists. Returns itself for\n\t * method chaining.\n\t */\n\tclear(key: K): this {\n\t\tconst cacheMap = this._cacheMap;\n\t\tif (cacheMap) {\n\t\t\tconst cacheKey = this._cacheKeyFn(key);\n\t\t\tcacheMap.delete(cacheKey);\n\t\t}\n\t\treturn this;\n\t}\n\n\t/**\n\t * Clears the entire cache. To be used when some event results in unknown\n\t * invalidations across this particular `DataLoader`. Returns itself for\n\t * method chaining.\n\t */\n\tclearAll(): this {\n\t\tconst cacheMap = this._cacheMap;\n\t\tif (cacheMap) {\n\t\t\tcacheMap.clear();\n\t\t}\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds the provided key and value to the cache. If the key already\n\t * exists, no change is made. Returns itself for method chaining.\n\t *\n\t * To prime the cache with an error at a key, provide an Error instance.\n\t */\n\tprime(key: K, value: V | Promise<V> | Error): this {\n\t\tconst cacheMap = this._cacheMap;\n\t\tif (cacheMap) {\n\t\t\tconst cacheKey = this._cacheKeyFn(key);\n\n\t\t\t// Only add the key if it does not already exist.\n\t\t\tif (cacheMap.get(cacheKey) === undefined) {\n\t\t\t\t// Cache a rejected promise if the value is an Error, in order to match\n\t\t\t\t// the behavior of load(key).\n\t\t\t\tlet promise;\n\t\t\t\tif (value instanceof Error) {\n\t\t\t\t\tpromise = Promise.reject(value);\n\t\t\t\t\t// Since this is a case where an Error is intentionally being primed\n\t\t\t\t\t// for a given key, we want to disable unhandled promise rejection.\n\t\t\t\t\tpromise.catch(() => {});\n\t\t\t\t} else {\n\t\t\t\t\tpromise = Promise.resolve(value);\n\t\t\t\t}\n\t\t\t\tcacheMap.set(cacheKey, promise);\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t}\n\n\t/**\n\t * The name given to this `DataLoader` instance. Useful for APM tools.\n\t *\n\t * Is `null` if not set in the constructor.\n\t */\n\tname: string | null;\n}\n\n// Private: Enqueue a Job to be executed after all \"PromiseJobs\" Jobs.\n//\n// ES6 JavaScript uses the concepts Job and JobQueue to schedule work to occur\n// after the current execution context has completed:\n// http://www.ecma-international.org/ecma-262/6.0/#sec-jobs-and-job-queues\n//\n// Node.js uses the `process.nextTick` mechanism to implement the concept of a\n// Job, maintaining a global FIFO JobQueue for all Jobs, which is flushed after\n// the current call stack ends.\n//\n// When calling `then` on a Promise, it enqueues a Job on a specific\n// \"PromiseJobs\" JobQueue which is flushed in Node as a single Job on the\n// global JobQueue.\n//\n// DataLoader batches all loads which occur in a single frame of execution, but\n// should include in the batch all loads which occur during the flushing of the\n// \"PromiseJobs\" JobQueue after that same execution frame.\n//\n// In order to avoid the DataLoader dispatch Job occuring before \"PromiseJobs\",\n// A Promise Job is created with the sole purpose of enqueuing a global Job,\n// ensuring that it always occurs after \"PromiseJobs\" ends.\n//\n// Node.js's job queue is unique. Browsers do not have an equivalent mechanism\n// for enqueuing a job to be performed after promise microtasks and before the\n// next macrotask. For browser environments, a macrotask is used (via\n// setImmediate or setTimeout) at a potential performance penalty.\nconst enqueuePostPromiseJob: (fn: () => void) => void =\n\t/** @ts-ignore */\n\ttypeof process === 'object' && typeof process.nextTick === 'function'\n\t\t? function (fn) {\n\t\t\t\tif (!resolvedPromise) {\n\t\t\t\t\tresolvedPromise = Promise.resolve();\n\t\t\t\t}\n\t\t\t\tresolvedPromise.then(() => {\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tprocess.nextTick(fn);\n\t\t\t\t});\n\t\t\t}\n\t\t: // @ts-ignore\n\t\t\ttypeof setImmediate === 'function'\n\t\t\t? function (fn) {\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tsetImmediate(fn);\n\t\t\t\t}\n\t\t\t: function (fn) {\n\t\t\t\t\tsetTimeout(fn);\n\t\t\t\t};\n\n// Private: cached resolved Promise instance\nlet resolvedPromise: Promise<void> | undefined;\n\n// Private: Describes a batch of requests\ntype Batch<K, V> = {\n\thasDispatched: boolean;\n\tkeys: Array<K>;\n\tcallbacks: Array<{\n\t\tresolve: (value: V) => void;\n\t\treject: (error: Error) => void;\n\t}>;\n\tcacheHits?: Array<() => void>;\n};\n\n// Private: Either returns the current batch, or creates and schedules a\n// dispatch of a new batch for the given loader.\nfunction getCurrentBatch<K, V>(loader: DataLoader<K, V, any>): Batch<K, V> {\n\t// If there is an existing batch which has not yet dispatched and is within\n\t// the limit of the batch size, then return it.\n\tconst existingBatch = loader._batch;\n\tif (\n\t\texistingBatch !== null &&\n\t\t!existingBatch.hasDispatched &&\n\t\texistingBatch.keys.length < loader._maxBatchSize\n\t) {\n\t\treturn existingBatch;\n\t}\n\n\t// Otherwise, create a new batch for this loader.\n\tconst newBatch = { hasDispatched: false, keys: [], callbacks: [] };\n\n\t// Store it on the loader so it may be reused.\n\tloader._batch = newBatch;\n\n\t// Then schedule a task to dispatch this batch of requests.\n\tloader._batchScheduleFn(() => {\n\t\tdispatchBatch(loader, newBatch);\n\t});\n\n\treturn newBatch;\n}\n\nfunction dispatchBatch<K, V>(loader: DataLoader<K, V, any>, batch: Batch<K, V>) {\n\t// Mark this batch as having been dispatched.\n\tbatch.hasDispatched = true;\n\n\t// If there's nothing to load, resolve any cache hits and return early.\n\tif (batch.keys.length === 0) {\n\t\tresolveCacheHits(batch);\n\t\treturn;\n\t}\n\n\t// Call the provided batchLoadFn for this loader with the batch's keys and\n\t// with the loader as the `this` context.\n\tlet batchPromise;\n\ttry {\n\t\tbatchPromise = loader._batchLoadFn(batch.keys);\n\t} catch (e) {\n\t\treturn failedDispatch(\n\t\t\tloader,\n\t\t\tbatch,\n\t\t\tnew TypeError(\n\t\t\t\t'DataLoader must be constructed with a function which accepts ' +\n\t\t\t\t\t'Array<key> and returns Promise<Array<value>>, but the function ' +\n\t\t\t\t\t`errored synchronously: ${String(e)}.`,\n\t\t\t),\n\t\t);\n\t}\n\n\t// Assert the expected response from batchLoadFn\n\tif (!batchPromise || typeof batchPromise.then !== 'function') {\n\t\treturn failedDispatch(\n\t\t\tloader,\n\t\t\tbatch,\n\t\t\tnew TypeError(\n\t\t\t\t'DataLoader must be constructed with a function which accepts ' +\n\t\t\t\t\t'Array<key> and returns Promise<Array<value>>, but the function did ' +\n\t\t\t\t\t`not return a Promise: ${String(batchPromise)}.`,\n\t\t\t),\n\t\t);\n\t}\n\n\t// Await the resolution of the call to batchLoadFn.\n\tPromise.resolve(batchPromise)\n\t\t.then((values) => {\n\t\t\t// Assert the expected resolution from batchLoadFn.\n\t\t\tif (!isArrayLike(values)) {\n\t\t\t\tthrow new TypeError(\n\t\t\t\t\t'DataLoader must be constructed with a function which accepts ' +\n\t\t\t\t\t\t'Array<key> and returns Promise<Array<value>>, but the function did ' +\n\t\t\t\t\t\t`not return a Promise of an Array: ${String(values)}.`,\n\t\t\t\t);\n\t\t\t}\n\t\t\tif (values.length !== batch.keys.length) {\n\t\t\t\tthrow new TypeError(\n\t\t\t\t\t'DataLoader must be constructed with a function which accepts ' +\n\t\t\t\t\t\t'Array<key> and returns Promise<Array<value>>, but the function did ' +\n\t\t\t\t\t\t'not return a Promise of an Array of the same length as the Array ' +\n\t\t\t\t\t\t'of keys.' +\n\t\t\t\t\t\t`\\n\\nKeys:\\n${String(batch.keys)}` +\n\t\t\t\t\t\t`\\n\\nValues:\\n${String(values)}`,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\t// Resolve all cache hits in the same micro-task as freshly loaded values.\n\t\t\tresolveCacheHits(batch);\n\n\t\t\t// Step through values, resolving or rejecting each Promise in the batch.\n\t\t\tfor (let i = 0; i < batch.callbacks.length; i++) {\n\t\t\t\tconst value = values[i];\n\t\t\t\tif (value instanceof Error) {\n\t\t\t\t\tbatch.callbacks[i].reject(value);\n\t\t\t\t} else {\n\t\t\t\t\tbatch.callbacks[i].resolve(value);\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t\t.catch((error: unknown) => {\n\t\t\tfailedDispatch(loader, batch, error as Error);\n\t\t});\n}\n\n// Private: do not cache individual loads if the entire batch dispatch fails,\n// but still reject each request so they do not hang.\nfunction failedDispatch<K, V>(loader: DataLoader<K, V, any>, batch: Batch<K, V>, error: Error) {\n\t// Cache hits are resolved, even though the batch failed.\n\tresolveCacheHits(batch);\n\tfor (let i = 0; i < batch.keys.length; i++) {\n\t\tloader.clear(batch.keys[i]);\n\t\tbatch.callbacks[i].reject(error);\n\t}\n}\n\n// Private: Resolves the Promises for any cache hits in this batch.\nfunction resolveCacheHits(batch: Batch<any, any>) {\n\tif (batch.cacheHits) {\n\t\tfor (let i = 0; i < batch.cacheHits.length; i++) {\n\t\t\tbatch.cacheHits[i]();\n\t\t}\n\t}\n}\n\n// Private: given the DataLoader's options, produce a valid max batch size.\nfunction getValidMaxBatchSize<K, V, C>(options?: DataLoader.Options<K, V, C>): number {\n\tconst shouldBatch = !options || options.batch !== false;\n\tif (!shouldBatch) {\n\t\treturn 1;\n\t}\n\tconst maxBatchSize = options && options.maxBatchSize;\n\tif (maxBatchSize === undefined) {\n\t\treturn Infinity;\n\t}\n\tif (typeof maxBatchSize !== 'number' || maxBatchSize < 1) {\n\t\tthrow new TypeError(`maxBatchSize must be a positive number: ${maxBatchSize}`);\n\t}\n\treturn maxBatchSize;\n}\n\n// Private\nfunction getValidBatchScheduleFn<K, V, C>(\n\toptions?: DataLoader.Options<K, V, C>,\n): (cb: () => void) => void {\n\tconst batchScheduleFn = options && options.batchScheduleFn;\n\tif (batchScheduleFn === undefined) {\n\t\treturn enqueuePostPromiseJob;\n\t}\n\tif (typeof batchScheduleFn !== 'function') {\n\t\tthrow new TypeError(`batchScheduleFn must be a function: ${batchScheduleFn}`);\n\t}\n\treturn batchScheduleFn;\n}\n\n// Private: given the DataLoader's options, produce a cache key function.\nfunction getValidCacheKeyFn<K, V, C>(options?: DataLoader.Options<K, V, C>): (key: K) => C {\n\tconst cacheKeyFn = options && options.cacheKeyFn;\n\tif (cacheKeyFn === undefined) {\n\t\treturn (key: K) => key as unknown as C;\n\t}\n\tif (typeof cacheKeyFn !== 'function') {\n\t\tthrow new TypeError(`cacheKeyFn must be a function: ${cacheKeyFn}`);\n\t}\n\treturn cacheKeyFn;\n}\n\n// Private: given the DataLoader's options, produce a CacheMap to be used.\nfunction getValidCacheMap<K, V, C>(\n\toptions?: DataLoader.Options<K, V, C>,\n): DataLoader.CacheMap<C, Promise<V>> | null {\n\tconst shouldCache = !options || options.cache !== false;\n\tif (!shouldCache) {\n\t\treturn null;\n\t}\n\tconst cacheMap = options && options.cacheMap;\n\tif (cacheMap === undefined) {\n\t\treturn new Map();\n\t}\n\tif (cacheMap !== null) {\n\t\tconst cacheFunctions = ['get', 'set', 'delete', 'clear'] as const;\n\t\tconst missingFunctions = cacheFunctions.filter(\n\t\t\t(fnName) => cacheMap && typeof cacheMap[fnName] !== 'function',\n\t\t);\n\t\tif (missingFunctions.length !== 0) {\n\t\t\tthrow new TypeError('Custom cacheMap missing methods: ' + missingFunctions.join(', '));\n\t\t}\n\t}\n\treturn cacheMap;\n}\n\nfunction getValidName<K, V, C>(options?: DataLoader.Options<K, V, C>): string | null {\n\tif (options && options.name) {\n\t\treturn options.name;\n\t}\n\n\treturn null;\n}\n\nfunction isArrayLike(x: unknown): x is ArrayLike<unknown> {\n\treturn (\n\t\ttypeof x === 'object' &&\n\t\tx !== null &&\n\t\t'length' in x &&\n\t\ttypeof x.length === 'number' &&\n\t\t(x.length === 0 || (x.length > 0 && Object.prototype.hasOwnProperty.call(x, x.length - 1)))\n\t);\n}\n\nexport declare namespace DataLoader {\n\t// If a custom cache is provided, it must be of this type (a subset of ES6 Map).\n\texport type CacheMap<K, V> = {\n\t\tget(key: K): V | void;\n\t\tset(key: K, value: V): any;\n\t\tdelete(key: K): any;\n\t\tclear(): any;\n\t};\n\n\t// A Function, which when given an Array of keys, returns a Promise of an Array\n\t// of values or Errors.\n\texport type BatchLoadFn<K, V> = (keys: ReadonlyArray<K>) => PromiseLike<ArrayLike<V | Error>>;\n\n\t// Optionally turn off batching or caching or provide a cache key function or a\n\t// custom cache instance.\n\texport type Options<K, V, C = K> = {\n\t\t/**\n\t\t * Default `true`. Set to `false` to disable batching, invoking\n\t\t * `batchLoadFn` with a single load key. This is equivalent to setting\n\t\t * `maxBatchSize` to `1`.\n\t\t */\n\t\tbatch?: boolean;\n\n\t\t/**\n\t\t * Default `Infinity`. Limits the number of items that get passed in to the\n\t\t * `batchLoadFn`. May be set to `1` to disable batching.\n\t\t */\n\t\tmaxBatchSize?: number;\n\n\t\t/**\n\t\t * Default see https://github.com/graphql/dataloader#batch-scheduling.\n\t\t * A function to schedule the later execution of a batch. The function is\n\t\t * expected to call the provided callback in the immediate future.\n\t\t */\n\t\tbatchScheduleFn?: (callback: () => void) => void;\n\n\t\t/**\n\t\t * Default `true`. Set to `false` to disable memoization caching, creating a\n\t\t * new Promise and new key in the `batchLoadFn` for every load of the same\n\t\t * key. This is equivalent to setting `cacheMap` to `null`.\n\t\t */\n\t\tcache?: boolean;\n\n\t\t/**\n\t\t * Default `key => key`. Produces cache key for a given load key. Useful\n\t\t * when keys are objects and two objects should be considered equivalent.\n\t\t */\n\t\tcacheKeyFn?: (key: K) => C;\n\n\t\t/**\n\t\t * Default `new Map()`. Instance of `Map` (or an object with a similar API)\n\t\t * to be used as cache. May be set to `null` to disable caching.\n\t\t */\n\t\tcacheMap?: CacheMap<C, Promise<V>> | null;\n\n\t\t/**\n\t\t * The name given to this `DataLoader` instance. Useful for APM tools.\n\t\t *\n\t\t * Is `null` if not set in the constructor.\n\t\t */\n\t\tname?: string | null;\n\t};\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\n// Helper utility: write number as an ULEB array.\n// Original code is taken from: https://www.npmjs.com/package/uleb128 (no longer exists)\nexport function ulebEncode(num: number): number[] {\n\tconst arr = [];\n\tlet len = 0;\n\n\tif (num === 0) {\n\t\treturn [0];\n\t}\n\n\twhile (num > 0) {\n\t\tarr[len] = num & 0x7f;\n\t\tif ((num >>= 7)) {\n\t\t\tarr[len] |= 0x80;\n\t\t}\n\t\tlen += 1;\n\t}\n\n\treturn arr;\n}\n\n// Helper utility: decode ULEB as an array of numbers.\n// Original code is taken from: https://www.npmjs.com/package/uleb128 (no longer exists)\nexport function ulebDecode(arr: number[] | Uint8Array): {\n\tvalue: number;\n\tlength: number;\n} {\n\tlet total = 0;\n\tlet shift = 0;\n\tlet len = 0;\n\n\t// eslint-disable-next-line no-constant-condition\n\twhile (true) {\n\t\tconst byte = arr[len];\n\t\tlen += 1;\n\t\ttotal |= (byte & 0x7f) << shift;\n\t\tif ((byte & 0x80) === 0) {\n\t\t\tbreak;\n\t\t}\n\t\tshift += 7;\n\t}\n\n\treturn {\n\t\tvalue: total,\n\t\tlength: len,\n\t};\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { ulebDecode } from './uleb.js';\n\n/**\n * Class used for reading BCS data chunk by chunk. Meant to be used\n * by some wrapper, which will make sure that data is valid and is\n * matching the desired format.\n *\n * @example\n * // data for this example is:\n * // { a: u8, b: u32, c: bool, d: u64 }\n *\n * let reader = new BcsReader(\"647f1a060001ffffe7890423c78a050102030405\");\n * let field1 = reader.read8();\n * let field2 = reader.read32();\n * let field3 = reader.read8() === '1'; // bool\n * let field4 = reader.read64();\n * // ....\n *\n * Reading vectors is another deal in bcs. To read a vector, you first need to read\n * its length using {@link readULEB}. Here's an example:\n * @example\n * // data encoded: { field: [1, 2, 3, 4, 5] }\n * let reader = new BcsReader(\"050102030405\");\n * let vec_length = reader.readULEB();\n * let elements = [];\n * for (let i = 0; i < vec_length; i++) {\n *   elements.push(reader.read8());\n * }\n * console.log(elements); // [1,2,3,4,5]\n *\n * @param {String} data HEX-encoded data (serialized BCS)\n */\nexport class BcsReader {\n\tprivate dataView: DataView;\n\tprivate bytePosition: number = 0;\n\n\t/**\n\t * @param {Uint8Array} data Data to use as a buffer.\n\t */\n\tconstructor(data: Uint8Array) {\n\t\tthis.dataView = new DataView(data.buffer, data.byteOffset, data.byteLength);\n\t}\n\t/**\n\t * Shift current cursor position by `bytes`.\n\t *\n\t * @param {Number} bytes Number of bytes to\n\t * @returns {this} Self for possible chaining.\n\t */\n\tshift(bytes: number) {\n\t\tthis.bytePosition += bytes;\n\t\treturn this;\n\t}\n\t/**\n\t * Read U8 value from the buffer and shift cursor by 1.\n\t * @returns\n\t */\n\tread8(): number {\n\t\tconst value = this.dataView.getUint8(this.bytePosition);\n\t\tthis.shift(1);\n\t\treturn value;\n\t}\n\t/**\n\t * Read U16 value from the buffer and shift cursor by 2.\n\t * @returns\n\t */\n\tread16(): number {\n\t\tconst value = this.dataView.getUint16(this.bytePosition, true);\n\t\tthis.shift(2);\n\t\treturn value;\n\t}\n\t/**\n\t * Read U32 value from the buffer and shift cursor by 4.\n\t * @returns\n\t */\n\tread32(): number {\n\t\tconst value = this.dataView.getUint32(this.bytePosition, true);\n\t\tthis.shift(4);\n\t\treturn value;\n\t}\n\t/**\n\t * Read U64 value from the buffer and shift cursor by 8.\n\t * @returns\n\t */\n\tread64(): string {\n\t\tconst value1 = this.read32();\n\t\tconst value2 = this.read32();\n\n\t\tconst result = value2.toString(16) + value1.toString(16).padStart(8, '0');\n\n\t\treturn BigInt('0x' + result).toString(10);\n\t}\n\t/**\n\t * Read U128 value from the buffer and shift cursor by 16.\n\t */\n\tread128(): string {\n\t\tconst value1 = BigInt(this.read64());\n\t\tconst value2 = BigInt(this.read64());\n\t\tconst result = value2.toString(16) + value1.toString(16).padStart(16, '0');\n\n\t\treturn BigInt('0x' + result).toString(10);\n\t}\n\t/**\n\t * Read U128 value from the buffer and shift cursor by 32.\n\t * @returns\n\t */\n\tread256(): string {\n\t\tconst value1 = BigInt(this.read128());\n\t\tconst value2 = BigInt(this.read128());\n\t\tconst result = value2.toString(16) + value1.toString(16).padStart(32, '0');\n\n\t\treturn BigInt('0x' + result).toString(10);\n\t}\n\t/**\n\t * Read `num` number of bytes from the buffer and shift cursor by `num`.\n\t * @param num Number of bytes to read.\n\t */\n\treadBytes(num: number): Uint8Array {\n\t\tconst start = this.bytePosition + this.dataView.byteOffset;\n\t\tconst value = new Uint8Array(this.dataView.buffer, start, num);\n\n\t\tthis.shift(num);\n\n\t\treturn value;\n\t}\n\t/**\n\t * Read ULEB value - an integer of varying size. Used for enum indexes and\n\t * vector lengths.\n\t * @returns {Number} The ULEB value.\n\t */\n\treadULEB(): number {\n\t\tconst start = this.bytePosition + this.dataView.byteOffset;\n\t\tconst buffer = new Uint8Array(this.dataView.buffer, start);\n\t\tconst { value, length } = ulebDecode(buffer);\n\n\t\tthis.shift(length);\n\n\t\treturn value;\n\t}\n\t/**\n\t * Read a BCS vector: read a length and then apply function `cb` X times\n\t * where X is the length of the vector, defined as ULEB in BCS bytes.\n\t * @param cb Callback to process elements of vector.\n\t * @returns {Array<Any>} Array of the resulting values, returned by callback.\n\t */\n\treadVec(cb: (reader: BcsReader, i: number, length: number) => any): any[] {\n\t\tconst length = this.readULEB();\n\t\tconst result = [];\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tresult.push(cb(this, i, length));\n\t\t}\n\t\treturn result;\n\t}\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase58, fromBase64, fromHex, toBase58, toBase64, toHex } from '@mysten/utils';\nimport type { Encoding } from './types.js';\n\n/**\n * Encode data with either `hex` or `base64`.\n *\n * @param {Uint8Array} data Data to encode.\n * @param {String} encoding Encoding to use: base64 or hex\n * @return {String} Encoded value.\n */\nexport function encodeStr(data: Uint8Array, encoding: Encoding): string {\n\tswitch (encoding) {\n\t\tcase 'base58':\n\t\t\treturn toBase58(data);\n\t\tcase 'base64':\n\t\t\treturn toBase64(data);\n\t\tcase 'hex':\n\t\t\treturn toHex(data);\n\t\tdefault:\n\t\t\tthrow new Error('Unsupported encoding, supported values are: base64, hex');\n\t}\n}\n\n/**\n * Decode either `base64` or `hex` data.\n *\n * @param {String} data Data to encode.\n * @param {String} encoding Encoding to use: base64 or hex\n * @return {Uint8Array} Encoded value.\n */\nexport function decodeStr(data: string, encoding: Encoding): Uint8Array {\n\tswitch (encoding) {\n\t\tcase 'base58':\n\t\t\treturn fromBase58(data);\n\t\tcase 'base64':\n\t\t\treturn fromBase64(data);\n\t\tcase 'hex':\n\t\t\treturn fromHex(data);\n\t\tdefault:\n\t\t\tthrow new Error('Unsupported encoding, supported values are: base64, hex');\n\t}\n}\n\nexport function splitGenericParameters(\n\tstr: string,\n\tgenericSeparators: [string, string] = ['<', '>'],\n) {\n\tconst [left, right] = genericSeparators;\n\tconst tok = [];\n\tlet word = '';\n\tlet nestedAngleBrackets = 0;\n\n\tfor (let i = 0; i < str.length; i++) {\n\t\tconst char = str[i];\n\t\tif (char === left) {\n\t\t\tnestedAngleBrackets++;\n\t\t}\n\t\tif (char === right) {\n\t\t\tnestedAngleBrackets--;\n\t\t}\n\t\tif (nestedAngleBrackets === 0 && char === ',') {\n\t\t\ttok.push(word.trim());\n\t\t\tword = '';\n\t\t\tcontinue;\n\t\t}\n\t\tword += char;\n\t}\n\n\ttok.push(word.trim());\n\n\treturn tok;\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { Encoding } from './types.js';\nimport { ulebEncode } from './uleb.js';\nimport { encodeStr } from './utils.js';\n\nexport interface BcsWriterOptions {\n\t/** The initial size (in bytes) of the buffer tht will be allocated */\n\tinitialSize?: number;\n\t/** The maximum size (in bytes) that the buffer is allowed to grow to */\n\tmaxSize?: number;\n\t/** The amount of bytes that will be allocated whenever additional memory is required */\n\tallocateSize?: number;\n}\n\n/**\n * Class used to write BCS data into a buffer. Initializer requires\n * some size of a buffer to init; default value for this buffer is 1KB.\n *\n * Most methods are chainable, so it is possible to write them in one go.\n *\n * @example\n * let serialized = new BcsWriter()\n *   .write8(10)\n *   .write32(1000000)\n *   .write64(10000001000000)\n *   .hex();\n */\n\n/**\n * Set of methods that allows data encoding/decoding as standalone\n * BCS value or a part of a composed structure/vector.\n */\nexport class BcsWriter {\n\tprivate dataView: DataView;\n\tprivate bytePosition: number = 0;\n\tprivate size: number;\n\tprivate maxSize: number;\n\tprivate allocateSize: number;\n\n\tconstructor({\n\t\tinitialSize = 1024,\n\t\tmaxSize = Infinity,\n\t\tallocateSize = 1024,\n\t}: BcsWriterOptions = {}) {\n\t\tthis.size = initialSize;\n\t\tthis.maxSize = maxSize;\n\t\tthis.allocateSize = allocateSize;\n\t\tthis.dataView = new DataView(new ArrayBuffer(initialSize));\n\t}\n\n\tprivate ensureSizeOrGrow(bytes: number) {\n\t\tconst requiredSize = this.bytePosition + bytes;\n\t\tif (requiredSize > this.size) {\n\t\t\tconst nextSize = Math.min(this.maxSize, this.size + this.allocateSize);\n\t\t\tif (requiredSize > nextSize) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Attempting to serialize to BCS, but buffer does not have enough size. Allocated size: ${this.size}, Max size: ${this.maxSize}, Required size: ${requiredSize}`,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.size = nextSize;\n\t\t\tconst nextBuffer = new ArrayBuffer(this.size);\n\t\t\tnew Uint8Array(nextBuffer).set(new Uint8Array(this.dataView.buffer));\n\t\t\tthis.dataView = new DataView(nextBuffer);\n\t\t}\n\t}\n\n\t/**\n\t * Shift current cursor position by `bytes`.\n\t *\n\t * @param {Number} bytes Number of bytes to\n\t * @returns {this} Self for possible chaining.\n\t */\n\tshift(bytes: number): this {\n\t\tthis.bytePosition += bytes;\n\t\treturn this;\n\t}\n\t/**\n\t * Write a U8 value into a buffer and shift cursor position by 1.\n\t * @param {Number} value Value to write.\n\t * @returns {this}\n\t */\n\twrite8(value: number | bigint): this {\n\t\tthis.ensureSizeOrGrow(1);\n\t\tthis.dataView.setUint8(this.bytePosition, Number(value));\n\t\treturn this.shift(1);\n\t}\n\t/**\n\t * Write a U16 value into a buffer and shift cursor position by 2.\n\t * @param {Number} value Value to write.\n\t * @returns {this}\n\t */\n\twrite16(value: number | bigint): this {\n\t\tthis.ensureSizeOrGrow(2);\n\t\tthis.dataView.setUint16(this.bytePosition, Number(value), true);\n\t\treturn this.shift(2);\n\t}\n\t/**\n\t * Write a U32 value into a buffer and shift cursor position by 4.\n\t * @param {Number} value Value to write.\n\t * @returns {this}\n\t */\n\twrite32(value: number | bigint): this {\n\t\tthis.ensureSizeOrGrow(4);\n\t\tthis.dataView.setUint32(this.bytePosition, Number(value), true);\n\t\treturn this.shift(4);\n\t}\n\t/**\n\t * Write a U64 value into a buffer and shift cursor position by 8.\n\t * @param {bigint} value Value to write.\n\t * @returns {this}\n\t */\n\twrite64(value: number | bigint): this {\n\t\ttoLittleEndian(BigInt(value), 8).forEach((el) => this.write8(el));\n\n\t\treturn this;\n\t}\n\t/**\n\t * Write a U128 value into a buffer and shift cursor position by 16.\n\t *\n\t * @param {bigint} value Value to write.\n\t * @returns {this}\n\t */\n\twrite128(value: number | bigint): this {\n\t\ttoLittleEndian(BigInt(value), 16).forEach((el) => this.write8(el));\n\n\t\treturn this;\n\t}\n\t/**\n\t * Write a U256 value into a buffer and shift cursor position by 16.\n\t *\n\t * @param {bigint} value Value to write.\n\t * @returns {this}\n\t */\n\twrite256(value: number | bigint): this {\n\t\ttoLittleEndian(BigInt(value), 32).forEach((el) => this.write8(el));\n\n\t\treturn this;\n\t}\n\t/**\n\t * Write a ULEB value into a buffer and shift cursor position by number of bytes\n\t * written.\n\t * @param {Number} value Value to write.\n\t * @returns {this}\n\t */\n\twriteULEB(value: number): this {\n\t\tulebEncode(value).forEach((el) => this.write8(el));\n\t\treturn this;\n\t}\n\t/**\n\t * Write a vector into a buffer by first writing the vector length and then calling\n\t * a callback on each passed value.\n\t *\n\t * @param {Array<Any>} vector Array of elements to write.\n\t * @param {WriteVecCb} cb Callback to call on each element of the vector.\n\t * @returns {this}\n\t */\n\twriteVec(vector: any[], cb: (writer: BcsWriter, el: any, i: number, len: number) => void): this {\n\t\tthis.writeULEB(vector.length);\n\t\tArray.from(vector).forEach((el, i) => cb(this, el, i, vector.length));\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds support for iterations over the object.\n\t * @returns {Uint8Array}\n\t */\n\t*[Symbol.iterator](): Iterator<number, Iterable<number>> {\n\t\tfor (let i = 0; i < this.bytePosition; i++) {\n\t\t\tyield this.dataView.getUint8(i);\n\t\t}\n\t\treturn this.toBytes();\n\t}\n\n\t/**\n\t * Get underlying buffer taking only value bytes (in case initial buffer size was bigger).\n\t * @returns {Uint8Array} Resulting bcs.\n\t */\n\ttoBytes(): Uint8Array {\n\t\treturn new Uint8Array(this.dataView.buffer.slice(0, this.bytePosition));\n\t}\n\n\t/**\n\t * Represent data as 'hex' or 'base64'\n\t * @param encoding Encoding to use: 'base64' or 'hex'\n\t */\n\ttoString(encoding: Encoding): string {\n\t\treturn encodeStr(this.toBytes(), encoding);\n\t}\n}\n\nfunction toLittleEndian(bigint: bigint, size: number) {\n\tconst result = new Uint8Array(size);\n\tlet i = 0;\n\twhile (bigint > 0) {\n\t\tresult[i] = Number(bigint % BigInt(256));\n\t\tbigint = bigint / BigInt(256);\n\t\ti += 1;\n\t}\n\treturn result;\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase58, fromBase64, toBase58, toBase64, fromHex, toHex } from '@mysten/utils';\nimport { BcsReader } from './reader.js';\nimport { ulebEncode } from './uleb.js';\nimport type { BcsWriterOptions } from './writer.js';\nimport { BcsWriter } from './writer.js';\n\nexport interface BcsTypeOptions<T, Input = T> {\n\tname?: string;\n\tvalidate?: (value: Input) => void;\n}\n\nexport class BcsType<T, Input = T> {\n\t$inferType!: T;\n\t$inferInput!: Input;\n\tname: string;\n\tread: (reader: BcsReader) => T;\n\tserializedSize: (value: Input, options?: BcsWriterOptions) => number | null;\n\tvalidate: (value: Input) => void;\n\t#write: (value: Input, writer: BcsWriter) => void;\n\t#serialize: (value: Input, options?: BcsWriterOptions) => Uint8Array;\n\n\tconstructor(\n\t\toptions: {\n\t\t\tname: string;\n\t\t\tread: (reader: B<PERSON><PERSON>eader) => T;\n\t\t\twrite: (value: Input, writer: BcsWriter) => void;\n\t\t\tserialize?: (value: Input, options?: BcsWriterOptions) => Uint8Array;\n\t\t\tserializedSize?: (value: Input) => number | null;\n\t\t\tvalidate?: (value: Input) => void;\n\t\t} & BcsTypeOptions<T, Input>,\n\t) {\n\t\tthis.name = options.name;\n\t\tthis.read = options.read;\n\t\tthis.serializedSize = options.serializedSize ?? (() => null);\n\t\tthis.#write = options.write;\n\t\tthis.#serialize =\n\t\t\toptions.serialize ??\n\t\t\t((value, options) => {\n\t\t\t\tconst writer = new BcsWriter({\n\t\t\t\t\tinitialSize: this.serializedSize(value) ?? undefined,\n\t\t\t\t\t...options,\n\t\t\t\t});\n\t\t\t\tthis.#write(value, writer);\n\t\t\t\treturn writer.toBytes();\n\t\t\t});\n\n\t\tthis.validate = options.validate ?? (() => {});\n\t}\n\n\twrite(value: Input, writer: BcsWriter) {\n\t\tthis.validate(value);\n\t\tthis.#write(value, writer);\n\t}\n\n\tserialize(value: Input, options?: BcsWriterOptions) {\n\t\tthis.validate(value);\n\t\treturn new SerializedBcs(this, this.#serialize(value, options));\n\t}\n\n\tparse(bytes: Uint8Array): T {\n\t\tconst reader = new BcsReader(bytes);\n\t\treturn this.read(reader);\n\t}\n\n\tfromHex(hex: string) {\n\t\treturn this.parse(fromHex(hex));\n\t}\n\n\tfromBase58(b64: string) {\n\t\treturn this.parse(fromBase58(b64));\n\t}\n\n\tfromBase64(b64: string) {\n\t\treturn this.parse(fromBase64(b64));\n\t}\n\n\ttransform<T2 = T, Input2 = Input>({\n\t\tname,\n\t\tinput,\n\t\toutput,\n\t\tvalidate,\n\t}: {\n\t\tinput?: (val: Input2) => Input;\n\t\toutput?: (value: T) => T2;\n\t} & BcsTypeOptions<T2, Input2>) {\n\t\treturn new BcsType<T2, Input2>({\n\t\t\tname: name ?? this.name,\n\t\t\tread: (reader) => (output ? output(this.read(reader)) : (this.read(reader) as never)),\n\t\t\twrite: (value, writer) => this.#write(input ? input(value) : (value as never), writer),\n\t\t\tserializedSize: (value) => this.serializedSize(input ? input(value) : (value as never)),\n\t\t\tserialize: (value, options) =>\n\t\t\t\tthis.#serialize(input ? input(value) : (value as never), options),\n\t\t\tvalidate: (value) => {\n\t\t\t\tvalidate?.(value);\n\t\t\t\tthis.validate(input ? input(value) : (value as never));\n\t\t\t},\n\t\t});\n\t}\n}\n\nconst SERIALIZED_BCS_BRAND = Symbol.for('@mysten/serialized-bcs') as never;\nexport function isSerializedBcs(obj: unknown): obj is SerializedBcs<unknown> {\n\treturn !!obj && typeof obj === 'object' && (obj as any)[SERIALIZED_BCS_BRAND] === true;\n}\n\nexport class SerializedBcs<T, Input = T> {\n\t#schema: BcsType<T, Input>;\n\t#bytes: Uint8Array;\n\n\t// Used to brand SerializedBcs so that they can be identified, even between multiple copies\n\t// of the @mysten/bcs package are installed\n\tget [SERIALIZED_BCS_BRAND]() {\n\t\treturn true;\n\t}\n\n\tconstructor(type: BcsType<T, Input>, schema: Uint8Array) {\n\t\tthis.#schema = type;\n\t\tthis.#bytes = schema;\n\t}\n\n\ttoBytes() {\n\t\treturn this.#bytes;\n\t}\n\n\ttoHex() {\n\t\treturn toHex(this.#bytes);\n\t}\n\n\ttoBase64() {\n\t\treturn toBase64(this.#bytes);\n\t}\n\n\ttoBase58() {\n\t\treturn toBase58(this.#bytes);\n\t}\n\n\tparse() {\n\t\treturn this.#schema.parse(this.#bytes);\n\t}\n}\n\nexport function fixedSizeBcsType<T, Input = T>({\n\tsize,\n\t...options\n}: {\n\tname: string;\n\tsize: number;\n\tread: (reader: BcsReader) => T;\n\twrite: (value: Input, writer: BcsWriter) => void;\n} & BcsTypeOptions<T, Input>) {\n\treturn new BcsType<T, Input>({\n\t\t...options,\n\t\tserializedSize: () => size,\n\t});\n}\n\nexport function uIntBcsType({\n\treadMethod,\n\twriteMethod,\n\t...options\n}: {\n\tname: string;\n\tsize: number;\n\treadMethod: `read${8 | 16 | 32}`;\n\twriteMethod: `write${8 | 16 | 32}`;\n\tmaxValue: number;\n} & BcsTypeOptions<number, number>) {\n\treturn fixedSizeBcsType<number>({\n\t\t...options,\n\t\tread: (reader) => reader[readMethod](),\n\t\twrite: (value, writer) => writer[writeMethod](value),\n\t\tvalidate: (value) => {\n\t\t\tif (value < 0 || value > options.maxValue) {\n\t\t\t\tthrow new TypeError(\n\t\t\t\t\t`Invalid ${options.name} value: ${value}. Expected value in range 0-${options.maxValue}`,\n\t\t\t\t);\n\t\t\t}\n\t\t\toptions.validate?.(value);\n\t\t},\n\t});\n}\n\nexport function bigUIntBcsType({\n\treadMethod,\n\twriteMethod,\n\t...options\n}: {\n\tname: string;\n\tsize: number;\n\treadMethod: `read${64 | 128 | 256}`;\n\twriteMethod: `write${64 | 128 | 256}`;\n\tmaxValue: bigint;\n} & BcsTypeOptions<string, string | number | bigint>) {\n\treturn fixedSizeBcsType<string, string | number | bigint>({\n\t\t...options,\n\t\tread: (reader) => reader[readMethod](),\n\t\twrite: (value, writer) => writer[writeMethod](BigInt(value)),\n\t\tvalidate: (val) => {\n\t\t\tconst value = BigInt(val);\n\t\t\tif (value < 0 || value > options.maxValue) {\n\t\t\t\tthrow new TypeError(\n\t\t\t\t\t`Invalid ${options.name} value: ${value}. Expected value in range 0-${options.maxValue}`,\n\t\t\t\t);\n\t\t\t}\n\t\t\toptions.validate?.(value);\n\t\t},\n\t});\n}\n\nexport function dynamicSizeBcsType<T, Input = T>({\n\tserialize,\n\t...options\n}: {\n\tname: string;\n\tread: (reader: BcsReader) => T;\n\tserialize: (value: Input, options?: BcsWriterOptions) => Uint8Array;\n} & BcsTypeOptions<T, Input>) {\n\tconst type = new BcsType<T, Input>({\n\t\t...options,\n\t\tserialize,\n\t\twrite: (value, writer) => {\n\t\t\tfor (const byte of type.serialize(value).toBytes()) {\n\t\t\t\twriter.write8(byte);\n\t\t\t}\n\t\t},\n\t});\n\n\treturn type;\n}\n\nexport function stringLikeBcsType({\n\ttoBytes,\n\tfromBytes,\n\t...options\n}: {\n\tname: string;\n\ttoBytes: (value: string) => Uint8Array;\n\tfromBytes: (bytes: Uint8Array) => string;\n\tserializedSize?: (value: string) => number | null;\n} & BcsTypeOptions<string>) {\n\treturn new BcsType<string>({\n\t\t...options,\n\t\tread: (reader) => {\n\t\t\tconst length = reader.readULEB();\n\t\t\tconst bytes = reader.readBytes(length);\n\n\t\t\treturn fromBytes(bytes);\n\t\t},\n\t\twrite: (hex, writer) => {\n\t\t\tconst bytes = toBytes(hex);\n\t\t\twriter.writeULEB(bytes.length);\n\t\t\tfor (let i = 0; i < bytes.length; i++) {\n\t\t\t\twriter.write8(bytes[i]);\n\t\t\t}\n\t\t},\n\t\tserialize: (value) => {\n\t\t\tconst bytes = toBytes(value);\n\t\t\tconst size = ulebEncode(bytes.length);\n\t\t\tconst result = new Uint8Array(size.length + bytes.length);\n\t\t\tresult.set(size, 0);\n\t\t\tresult.set(bytes, size.length);\n\n\t\t\treturn result;\n\t\t},\n\t\tvalidate: (value) => {\n\t\t\tif (typeof value !== 'string') {\n\t\t\t\tthrow new TypeError(`Invalid ${options.name} value: ${value}. Expected string`);\n\t\t\t}\n\t\t\toptions.validate?.(value);\n\t\t},\n\t});\n}\n\nexport function lazyBcsType<T, Input>(cb: () => BcsType<T, Input>) {\n\tlet lazyType: BcsType<T, Input> | null = null;\n\tfunction getType() {\n\t\tif (!lazyType) {\n\t\t\tlazyType = cb();\n\t\t}\n\t\treturn lazyType;\n\t}\n\n\treturn new BcsType<T, Input>({\n\t\tname: 'lazy' as never,\n\t\tread: (data) => getType().read(data),\n\t\tserializedSize: (value) => getType().serializedSize(value),\n\t\twrite: (value, writer) => getType().write(value, writer),\n\t\tserialize: (value, options) => getType().serialize(value, options).toBytes(),\n\t});\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { BcsTypeOptions } from './bcs-type.js';\nimport {\n\tBcsType,\n\tbigUIntBcsType,\n\tdynamicSizeBcsType,\n\tfixedSizeBcsType,\n\tlazyBcsType,\n\tstringLikeBcsType,\n\tuIntBcsType,\n} from './bcs-type.js';\nimport type { EnumInputShape, EnumOutputShape } from './types.js';\nimport { ulebEncode } from './uleb.js';\n\nexport const bcs = {\n\t/**\n\t * Creates a BcsType that can be used to read and write an 8-bit unsigned integer.\n\t * @example\n\t * bcs.u8().serialize(255).toBytes() // Uint8Array [ 255 ]\n\t */\n\tu8(options?: BcsTypeOptions<number>) {\n\t\treturn uIntBcsType({\n\t\t\tname: 'u8',\n\t\t\treadMethod: 'read8',\n\t\t\twriteMethod: 'write8',\n\t\t\tsize: 1,\n\t\t\tmaxValue: 2 ** 8 - 1,\n\t\t\t...options,\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType that can be used to read and write a 16-bit unsigned integer.\n\t * @example\n\t * bcs.u16().serialize(65535).toBytes() // Uint8Array [ 255, 255 ]\n\t */\n\tu16(options?: BcsTypeOptions<number>) {\n\t\treturn uIntBcsType({\n\t\t\tname: 'u16',\n\t\t\treadMethod: 'read16',\n\t\t\twriteMethod: 'write16',\n\t\t\tsize: 2,\n\t\t\tmaxValue: 2 ** 16 - 1,\n\t\t\t...options,\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType that can be used to read and write a 32-bit unsigned integer.\n\t * @example\n\t * bcs.u32().serialize(4294967295).toBytes() // Uint8Array [ 255, 255, 255, 255 ]\n\t */\n\tu32(options?: BcsTypeOptions<number>) {\n\t\treturn uIntBcsType({\n\t\t\tname: 'u32',\n\t\t\treadMethod: 'read32',\n\t\t\twriteMethod: 'write32',\n\t\t\tsize: 4,\n\t\t\tmaxValue: 2 ** 32 - 1,\n\t\t\t...options,\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType that can be used to read and write a 64-bit unsigned integer.\n\t * @example\n\t * bcs.u64().serialize(1).toBytes() // Uint8Array [ 1, 0, 0, 0, 0, 0, 0, 0 ]\n\t */\n\tu64(options?: BcsTypeOptions<string, number | bigint | string>) {\n\t\treturn bigUIntBcsType({\n\t\t\tname: 'u64',\n\t\t\treadMethod: 'read64',\n\t\t\twriteMethod: 'write64',\n\t\t\tsize: 8,\n\t\t\tmaxValue: 2n ** 64n - 1n,\n\t\t\t...options,\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType that can be used to read and write a 128-bit unsigned integer.\n\t * @example\n\t * bcs.u128().serialize(1).toBytes() // Uint8Array [ 1, ..., 0 ]\n\t */\n\tu128(options?: BcsTypeOptions<string, number | bigint | string>) {\n\t\treturn bigUIntBcsType({\n\t\t\tname: 'u128',\n\t\t\treadMethod: 'read128',\n\t\t\twriteMethod: 'write128',\n\t\t\tsize: 16,\n\t\t\tmaxValue: 2n ** 128n - 1n,\n\t\t\t...options,\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType that can be used to read and write a 256-bit unsigned integer.\n\t * @example\n\t * bcs.u256().serialize(1).toBytes() // Uint8Array [ 1, ..., 0 ]\n\t */\n\tu256(options?: BcsTypeOptions<string, number | bigint | string>) {\n\t\treturn bigUIntBcsType({\n\t\t\tname: 'u256',\n\t\t\treadMethod: 'read256',\n\t\t\twriteMethod: 'write256',\n\t\t\tsize: 32,\n\t\t\tmaxValue: 2n ** 256n - 1n,\n\t\t\t...options,\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType that can be used to read and write boolean values.\n\t * @example\n\t * bcs.bool().serialize(true).toBytes() // Uint8Array [ 1 ]\n\t */\n\tbool(options?: BcsTypeOptions<boolean>) {\n\t\treturn fixedSizeBcsType<boolean>({\n\t\t\tname: 'bool',\n\t\t\tsize: 1,\n\t\t\tread: (reader) => reader.read8() === 1,\n\t\t\twrite: (value, writer) => writer.write8(value ? 1 : 0),\n\t\t\t...options,\n\t\t\tvalidate: (value) => {\n\t\t\t\toptions?.validate?.(value);\n\t\t\t\tif (typeof value !== 'boolean') {\n\t\t\t\t\tthrow new TypeError(`Expected boolean, found ${typeof value}`);\n\t\t\t\t}\n\t\t\t},\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType that can be used to read and write unsigned LEB encoded integers\n\t * @example\n\t *\n\t */\n\tuleb128(options?: BcsTypeOptions<number>) {\n\t\treturn dynamicSizeBcsType<number>({\n\t\t\tname: 'uleb128',\n\t\t\tread: (reader) => reader.readULEB(),\n\t\t\tserialize: (value) => {\n\t\t\t\treturn Uint8Array.from(ulebEncode(value));\n\t\t\t},\n\t\t\t...options,\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType representing a fixed length byte array\n\t * @param size The number of bytes this types represents\n\t * @example\n\t * bcs.bytes(3).serialize(new Uint8Array([1, 2, 3])).toBytes() // Uint8Array [1, 2, 3]\n\t */\n\tbytes<T extends number>(size: T, options?: BcsTypeOptions<Uint8Array, Iterable<number>>) {\n\t\treturn fixedSizeBcsType<Uint8Array, Iterable<number>>({\n\t\t\tname: `bytes[${size}]`,\n\t\t\tsize,\n\t\t\tread: (reader) => reader.readBytes(size),\n\t\t\twrite: (value, writer) => {\n\t\t\t\tconst array = new Uint8Array(value);\n\t\t\t\tfor (let i = 0; i < size; i++) {\n\t\t\t\t\twriter.write8(array[i] ?? 0);\n\t\t\t\t}\n\t\t\t},\n\t\t\t...options,\n\t\t\tvalidate: (value) => {\n\t\t\t\toptions?.validate?.(value);\n\t\t\t\tif (!value || typeof value !== 'object' || !('length' in value)) {\n\t\t\t\t\tthrow new TypeError(`Expected array, found ${typeof value}`);\n\t\t\t\t}\n\t\t\t\tif (value.length !== size) {\n\t\t\t\t\tthrow new TypeError(`Expected array of length ${size}, found ${value.length}`);\n\t\t\t\t}\n\t\t\t},\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType representing a variable length byte array\n\t *\n\t * @example\n\t * bcs.byteVector().serialize([1, 2, 3]).toBytes() // Uint8Array [3, 1, 2, 3]\n\t */\n\tbyteVector(options?: BcsTypeOptions<Uint8Array, Iterable<number>>) {\n\t\treturn new BcsType<Uint8Array, Iterable<number>>({\n\t\t\tname: `bytesVector`,\n\t\t\tread: (reader) => {\n\t\t\t\tconst length = reader.readULEB();\n\n\t\t\t\treturn reader.readBytes(length);\n\t\t\t},\n\t\t\twrite: (value, writer) => {\n\t\t\t\tconst array = new Uint8Array(value);\n\t\t\t\twriter.writeULEB(array.length);\n\t\t\t\tfor (let i = 0; i < array.length; i++) {\n\t\t\t\t\twriter.write8(array[i] ?? 0);\n\t\t\t\t}\n\t\t\t},\n\t\t\t...options,\n\t\t\tserializedSize: (value) => {\n\t\t\t\tconst length = 'length' in value ? (value.length as number) : null;\n\t\t\t\treturn length == null ? null : ulebEncode(length).length + length;\n\t\t\t},\n\t\t\tvalidate: (value) => {\n\t\t\t\toptions?.validate?.(value);\n\t\t\t\tif (!value || typeof value !== 'object' || !('length' in value)) {\n\t\t\t\t\tthrow new TypeError(`Expected array, found ${typeof value}`);\n\t\t\t\t}\n\t\t\t},\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType that can ser/de string values.  Strings will be UTF-8 encoded\n\t * @example\n\t * bcs.string().serialize('a').toBytes() // Uint8Array [ 1, 97 ]\n\t */\n\tstring(options?: BcsTypeOptions<string>) {\n\t\treturn stringLikeBcsType({\n\t\t\tname: 'string',\n\t\t\ttoBytes: (value) => new TextEncoder().encode(value),\n\t\t\tfromBytes: (bytes) => new TextDecoder().decode(bytes),\n\t\t\t...options,\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType that represents a fixed length array of a given type\n\t * @param size The number of elements in the array\n\t * @param type The BcsType of each element in the array\n\t * @example\n\t * bcs.fixedArray(3, bcs.u8()).serialize([1, 2, 3]).toBytes() // Uint8Array [ 1, 2, 3 ]\n\t */\n\tfixedArray<T, Input>(\n\t\tsize: number,\n\t\ttype: BcsType<T, Input>,\n\t\toptions?: BcsTypeOptions<T[], Iterable<Input> & { length: number }>,\n\t) {\n\t\treturn new BcsType<T[], Iterable<Input> & { length: number }>({\n\t\t\tname: `${type.name}[${size}]`,\n\t\t\tread: (reader) => {\n\t\t\t\tconst result: T[] = new Array(size);\n\t\t\t\tfor (let i = 0; i < size; i++) {\n\t\t\t\t\tresult[i] = type.read(reader);\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t},\n\t\t\twrite: (value, writer) => {\n\t\t\t\tfor (const item of value) {\n\t\t\t\t\ttype.write(item, writer);\n\t\t\t\t}\n\t\t\t},\n\t\t\t...options,\n\t\t\tvalidate: (value) => {\n\t\t\t\toptions?.validate?.(value);\n\t\t\t\tif (!value || typeof value !== 'object' || !('length' in value)) {\n\t\t\t\t\tthrow new TypeError(`Expected array, found ${typeof value}`);\n\t\t\t\t}\n\t\t\t\tif (value.length !== size) {\n\t\t\t\t\tthrow new TypeError(`Expected array of length ${size}, found ${value.length}`);\n\t\t\t\t}\n\t\t\t},\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType representing an optional value\n\t * @param type The BcsType of the optional value\n\t * @example\n\t * bcs.option(bcs.u8()).serialize(null).toBytes() // Uint8Array [ 0 ]\n\t * bcs.option(bcs.u8()).serialize(1).toBytes() // Uint8Array [ 1, 1 ]\n\t */\n\toption<T, Input>(type: BcsType<T, Input>) {\n\t\treturn bcs\n\t\t\t.enum(`Option<${type.name}>`, {\n\t\t\t\tNone: null,\n\t\t\t\tSome: type,\n\t\t\t})\n\t\t\t.transform({\n\t\t\t\tinput: (value: Input | null | undefined) => {\n\t\t\t\t\tif (value == null) {\n\t\t\t\t\t\treturn { None: true };\n\t\t\t\t\t}\n\n\t\t\t\t\treturn { Some: value };\n\t\t\t\t},\n\t\t\t\toutput: (value) => {\n\t\t\t\t\tif (value.$kind === 'Some') {\n\t\t\t\t\t\treturn value.Some;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn null;\n\t\t\t\t},\n\t\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType representing a variable length vector of a given type\n\t * @param type The BcsType of each element in the vector\n\t *\n\t * @example\n\t * bcs.vector(bcs.u8()).toBytes([1, 2, 3]) // Uint8Array [ 3, 1, 2, 3 ]\n\t */\n\tvector<T, Input>(\n\t\ttype: BcsType<T, Input>,\n\t\toptions?: BcsTypeOptions<T[], Iterable<Input> & { length: number }>,\n\t) {\n\t\treturn new BcsType<T[], Iterable<Input> & { length: number }>({\n\t\t\tname: `vector<${type.name}>`,\n\t\t\tread: (reader) => {\n\t\t\t\tconst length = reader.readULEB();\n\t\t\t\tconst result: T[] = new Array(length);\n\t\t\t\tfor (let i = 0; i < length; i++) {\n\t\t\t\t\tresult[i] = type.read(reader);\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t},\n\t\t\twrite: (value, writer) => {\n\t\t\t\twriter.writeULEB(value.length);\n\t\t\t\tfor (const item of value) {\n\t\t\t\t\ttype.write(item, writer);\n\t\t\t\t}\n\t\t\t},\n\t\t\t...options,\n\t\t\tvalidate: (value) => {\n\t\t\t\toptions?.validate?.(value);\n\t\t\t\tif (!value || typeof value !== 'object' || !('length' in value)) {\n\t\t\t\t\tthrow new TypeError(`Expected array, found ${typeof value}`);\n\t\t\t\t}\n\t\t\t},\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType representing a tuple of a given set of types\n\t * @param types The BcsTypes for each element in the tuple\n\t *\n\t * @example\n\t * const tuple = bcs.tuple([bcs.u8(), bcs.string(), bcs.bool()])\n\t * tuple.serialize([1, 'a', true]).toBytes() // Uint8Array [ 1, 1, 97, 1 ]\n\t */\n\ttuple<const Types extends readonly BcsType<any>[]>(\n\t\ttypes: Types,\n\t\toptions?: BcsTypeOptions<\n\t\t\t{\n\t\t\t\t-readonly [K in keyof Types]: Types[K] extends BcsType<infer T, any> ? T : never;\n\t\t\t},\n\t\t\t{\n\t\t\t\t[K in keyof Types]: Types[K] extends BcsType<any, infer T> ? T : never;\n\t\t\t}\n\t\t>,\n\t) {\n\t\treturn new BcsType<\n\t\t\t{\n\t\t\t\t-readonly [K in keyof Types]: Types[K] extends BcsType<infer T, any> ? T : never;\n\t\t\t},\n\t\t\t{\n\t\t\t\t[K in keyof Types]: Types[K] extends BcsType<any, infer T> ? T : never;\n\t\t\t}\n\t\t>({\n\t\t\tname: `(${types.map((t) => t.name).join(', ')})`,\n\t\t\tserializedSize: (values) => {\n\t\t\t\tlet total = 0;\n\t\t\t\tfor (let i = 0; i < types.length; i++) {\n\t\t\t\t\tconst size = types[i].serializedSize(values[i]);\n\t\t\t\t\tif (size == null) {\n\t\t\t\t\t\treturn null;\n\t\t\t\t\t}\n\n\t\t\t\t\ttotal += size;\n\t\t\t\t}\n\n\t\t\t\treturn total;\n\t\t\t},\n\t\t\tread: (reader) => {\n\t\t\t\tconst result: unknown[] = [];\n\t\t\t\tfor (const type of types) {\n\t\t\t\t\tresult.push(type.read(reader));\n\t\t\t\t}\n\t\t\t\treturn result as never;\n\t\t\t},\n\t\t\twrite: (value, writer) => {\n\t\t\t\tfor (let i = 0; i < types.length; i++) {\n\t\t\t\t\ttypes[i].write(value[i], writer);\n\t\t\t\t}\n\t\t\t},\n\t\t\t...options,\n\t\t\tvalidate: (value) => {\n\t\t\t\toptions?.validate?.(value);\n\t\t\t\tif (!Array.isArray(value)) {\n\t\t\t\t\tthrow new TypeError(`Expected array, found ${typeof value}`);\n\t\t\t\t}\n\t\t\t\tif (value.length !== types.length) {\n\t\t\t\t\tthrow new TypeError(`Expected array of length ${types.length}, found ${value.length}`);\n\t\t\t\t}\n\t\t\t},\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType representing a struct of a given set of fields\n\t * @param name The name of the struct\n\t * @param fields The fields of the struct. The order of the fields affects how data is serialized and deserialized\n\t *\n\t * @example\n\t * const struct = bcs.struct('MyStruct', {\n\t *  a: bcs.u8(),\n\t *  b: bcs.string(),\n\t * })\n\t * struct.serialize({ a: 1, b: 'a' }).toBytes() // Uint8Array [ 1, 1, 97 ]\n\t */\n\tstruct<T extends Record<string, BcsType<any>>>(\n\t\tname: string,\n\t\tfields: T,\n\t\toptions?: Omit<\n\t\t\tBcsTypeOptions<\n\t\t\t\t{\n\t\t\t\t\t[K in keyof T]: T[K] extends BcsType<infer U, any> ? U : never;\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t[K in keyof T]: T[K] extends BcsType<any, infer U> ? U : never;\n\t\t\t\t}\n\t\t\t>,\n\t\t\t'name'\n\t\t>,\n\t) {\n\t\tconst canonicalOrder = Object.entries(fields);\n\n\t\treturn new BcsType<\n\t\t\t{\n\t\t\t\t[K in keyof T]: T[K] extends BcsType<infer U, any> ? U : never;\n\t\t\t},\n\t\t\t{\n\t\t\t\t[K in keyof T]: T[K] extends BcsType<any, infer U> ? U : never;\n\t\t\t}\n\t\t>({\n\t\t\tname,\n\t\t\tserializedSize: (values) => {\n\t\t\t\tlet total = 0;\n\t\t\t\tfor (const [field, type] of canonicalOrder) {\n\t\t\t\t\tconst size = type.serializedSize(values[field]);\n\t\t\t\t\tif (size == null) {\n\t\t\t\t\t\treturn null;\n\t\t\t\t\t}\n\n\t\t\t\t\ttotal += size;\n\t\t\t\t}\n\n\t\t\t\treturn total;\n\t\t\t},\n\t\t\tread: (reader) => {\n\t\t\t\tconst result: Record<string, unknown> = {};\n\t\t\t\tfor (const [field, type] of canonicalOrder) {\n\t\t\t\t\tresult[field] = type.read(reader);\n\t\t\t\t}\n\n\t\t\t\treturn result as never;\n\t\t\t},\n\t\t\twrite: (value, writer) => {\n\t\t\t\tfor (const [field, type] of canonicalOrder) {\n\t\t\t\t\ttype.write(value[field], writer);\n\t\t\t\t}\n\t\t\t},\n\t\t\t...options,\n\t\t\tvalidate: (value) => {\n\t\t\t\toptions?.validate?.(value);\n\t\t\t\tif (typeof value !== 'object' || value == null) {\n\t\t\t\t\tthrow new TypeError(`Expected object, found ${typeof value}`);\n\t\t\t\t}\n\t\t\t},\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType representing an enum of a given set of options\n\t * @param name The name of the enum\n\t * @param values The values of the enum. The order of the values affects how data is serialized and deserialized.\n\t * null can be used to represent a variant with no data.\n\t *\n\t * @example\n\t * const enum = bcs.enum('MyEnum', {\n\t *   A: bcs.u8(),\n\t *   B: bcs.string(),\n\t *   C: null,\n\t * })\n\t * enum.serialize({ A: 1 }).toBytes() // Uint8Array [ 0, 1 ]\n\t * enum.serialize({ B: 'a' }).toBytes() // Uint8Array [ 1, 1, 97 ]\n\t * enum.serialize({ C: true }).toBytes() // Uint8Array [ 2 ]\n\t */\n\tenum<T extends Record<string, BcsType<any> | null>>(\n\t\tname: string,\n\t\tvalues: T,\n\t\toptions?: Omit<\n\t\t\tBcsTypeOptions<\n\t\t\t\tEnumOutputShape<{\n\t\t\t\t\t[K in keyof T]: T[K] extends BcsType<infer U, any> ? U : true;\n\t\t\t\t}>,\n\t\t\t\tEnumInputShape<{\n\t\t\t\t\t[K in keyof T]: T[K] extends BcsType<any, infer U> ? U : boolean | object | null;\n\t\t\t\t}>\n\t\t\t>,\n\t\t\t'name'\n\t\t>,\n\t) {\n\t\tconst canonicalOrder = Object.entries(values as object);\n\t\treturn new BcsType<\n\t\t\tEnumOutputShape<{\n\t\t\t\t[K in keyof T]: T[K] extends BcsType<infer U, any> ? U : true;\n\t\t\t}>,\n\t\t\tEnumInputShape<{\n\t\t\t\t[K in keyof T]: T[K] extends BcsType<any, infer U> ? U : boolean | object | null;\n\t\t\t}>\n\t\t>({\n\t\t\tname,\n\t\t\tread: (reader) => {\n\t\t\t\tconst index = reader.readULEB();\n\n\t\t\t\tconst enumEntry = canonicalOrder[index];\n\t\t\t\tif (!enumEntry) {\n\t\t\t\t\tthrow new TypeError(`Unknown value ${index} for enum ${name}`);\n\t\t\t\t}\n\n\t\t\t\tconst [kind, type] = enumEntry;\n\n\t\t\t\treturn {\n\t\t\t\t\t[kind]: type?.read(reader) ?? true,\n\t\t\t\t\t$kind: kind,\n\t\t\t\t} as never;\n\t\t\t},\n\t\t\twrite: (value, writer) => {\n\t\t\t\tconst [name, val] = Object.entries(value).filter(([name]) =>\n\t\t\t\t\tObject.hasOwn(values, name),\n\t\t\t\t)[0];\n\n\t\t\t\tfor (let i = 0; i < canonicalOrder.length; i++) {\n\t\t\t\t\tconst [optionName, optionType] = canonicalOrder[i];\n\t\t\t\t\tif (optionName === name) {\n\t\t\t\t\t\twriter.writeULEB(i);\n\t\t\t\t\t\toptionType?.write(val, writer);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t...options,\n\t\t\tvalidate: (value) => {\n\t\t\t\toptions?.validate?.(value);\n\t\t\t\tif (typeof value !== 'object' || value == null) {\n\t\t\t\t\tthrow new TypeError(`Expected object, found ${typeof value}`);\n\t\t\t\t}\n\n\t\t\t\tconst keys = Object.keys(value).filter(\n\t\t\t\t\t(k) => value[k] !== undefined && Object.hasOwn(values, k),\n\t\t\t\t);\n\n\t\t\t\tif (keys.length !== 1) {\n\t\t\t\t\tthrow new TypeError(\n\t\t\t\t\t\t`Expected object with one key, but found ${keys.length} for type ${name}}`,\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\tconst [variant] = keys;\n\n\t\t\t\tif (!Object.hasOwn(values, variant)) {\n\t\t\t\t\tthrow new TypeError(`Invalid enum variant ${variant}`);\n\t\t\t\t}\n\t\t\t},\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType representing a map of a given key and value type\n\t * @param keyType The BcsType of the key\n\t * @param valueType The BcsType of the value\n\t * @example\n\t * const map = bcs.map(bcs.u8(), bcs.string())\n\t * map.serialize(new Map([[2, 'a']])).toBytes() // Uint8Array [ 1, 2, 1, 97 ]\n\t */\n\tmap<K, V, InputK = K, InputV = V>(keyType: BcsType<K, InputK>, valueType: BcsType<V, InputV>) {\n\t\treturn bcs.vector(bcs.tuple([keyType, valueType])).transform({\n\t\t\tname: `Map<${keyType.name}, ${valueType.name}>`,\n\t\t\tinput: (value: Map<InputK, InputV>) => {\n\t\t\t\treturn [...value.entries()];\n\t\t\t},\n\t\t\toutput: (value) => {\n\t\t\t\tconst result = new Map<K, V>();\n\t\t\t\tfor (const [key, val] of value) {\n\t\t\t\t\tresult.set(key, val);\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t},\n\t\t});\n\t},\n\n\t/**\n\t * Creates a BcsType that wraps another BcsType which is lazily evaluated. This is useful for creating recursive types.\n\t * @param cb A callback that returns the BcsType\n\t */\n\tlazy<T extends BcsType<any>>(cb: () => T): T {\n\t\treturn lazyBcsType(cb) as T;\n\t},\n};\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\n/*\n * BCS implementation {@see https://github.com/diem/bcs } for JavaScript.\n * Intended to be used for Move applications; supports both NodeJS and browser.\n *\n * For more details and examples {@see README.md }.\n *\n * @module bcs\n * @property {BcsReader}\n */\n\nimport { toBase58, fromBase58, toBase64, fromBase64, toHex, fromHex } from '@mysten/utils';\nimport type { BcsTypeOptions } from './bcs-type.js';\nimport { BcsType, isSerializedBcs, SerializedBcs } from './bcs-type.js';\nimport { bcs } from './bcs.js';\nimport { BcsReader } from './reader.js';\nimport type {\n\tEnumInputShape,\n\tEnumOutputShape,\n\tEnumOutputShapeWithKeys,\n\tInferBcsInput,\n\tInferBcsType,\n} from './types.js';\nimport { decodeStr, encodeStr, splitGenericParameters } from './utils.js';\nimport type { BcsWriterOptions } from './writer.js';\nimport { BcsWriter } from './writer.js';\n\n// Re-export all encoding dependencies.\nexport {\n\tbcs,\n\tBcsType,\n\ttype BcsTypeOptions,\n\tSerializedBcs,\n\tisSerializedBcs,\n\ttoBase58,\n\tfromBase58,\n\ttoBase64,\n\tfromBase64,\n\ttoHex,\n\tfromHex,\n\tencodeStr,\n\tdecodeStr,\n\tsplitGenericParameters,\n\tBcsReader,\n\tBcsWriter,\n\ttype BcsWriterOptions,\n\ttype InferBcsInput,\n\ttype InferBcsType,\n\ttype EnumOutputShape,\n\ttype EnumInputShape,\n\ttype EnumOutputShapeWithKeys,\n};\n\n/** @deprecated use toBase58 instead */\nexport const toB58 = toBase58;\n\n/** @deprecated use fromBase58 instead */\nexport const fromB58 = fromBase58;\n\n/** @deprecated use toBase64 instead */\nexport const toB64 = toBase64;\n\n/** @deprecated use fromBase64 instead */\nexport const fromB64 = fromBase64;\n\n/** @deprecated use toHex instead */\nexport const toHEX = toHex;\n\n/** @deprecated use fromHex instead */\nexport const fromHEX = fromHex;\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nconst SUI_NS_NAME_REGEX =\n\t/^(?!.*(^(?!@)|[-.@])($|[-.@]))(?:[a-z0-9-]{0,63}(?:\\.[a-z0-9-]{0,63})*)?@[a-z0-9-]{0,63}$/i;\nconst SUI_NS_DOMAIN_REGEX = /^(?!.*(^|[-.])($|[-.]))(?:[a-z0-9-]{0,63}\\.)+sui$/i;\nconst MAX_SUI_NS_NAME_LENGTH = 235;\n\nexport function isValidSuiNSName(name: string): boolean {\n\tif (name.length > MAX_SUI_NS_NAME_LENGTH) {\n\t\treturn false;\n\t}\n\n\tif (name.includes('@')) {\n\t\treturn SUI_NS_NAME_REGEX.test(name);\n\t}\n\n\treturn SUI_NS_DOMAIN_REGEX.test(name);\n}\n\nexport function normalizeSuiNSName(name: string, format: 'at' | 'dot' = 'at'): string {\n\tconst lowerCase = name.toLowerCase();\n\tlet parts;\n\n\tif (lowerCase.includes('@')) {\n\t\tif (!SUI_NS_NAME_REGEX.test(lowerCase)) {\n\t\t\tthrow new Error(`Invalid SuiNS name ${name}`);\n\t\t}\n\t\tconst [labels, domain] = lowerCase.split('@');\n\t\tparts = [...(labels ? labels.split('.') : []), domain];\n\t} else {\n\t\tif (!SUI_NS_DOMAIN_REGEX.test(lowerCase)) {\n\t\t\tthrow new Error(`Invalid SuiNS name ${name}`);\n\t\t}\n\t\tparts = lowerCase.split('.').slice(0, -1);\n\t}\n\n\tif (format === 'dot') {\n\t\treturn `${parts.join('.')}.sui`;\n\t}\n\n\treturn `${parts.slice(0, -1).join('.')}@${parts[parts.length - 1]}`;\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { isValidSuiNSName } from './suins.js';\n\n/** The pattern to find an optionally versioned name */\nconst NAME_PATTERN = /^([a-z0-9]+(?:-[a-z0-9]+)*)$/;\n/** The pattern for a valid version number */\nconst VERSION_REGEX = /^\\d+$/;\n/** The maximum size for an app */\nconst MAX_APP_SIZE = 64;\n/** The separator for the name */\nconst NAME_SEPARATOR = '/';\n\nexport const isValidNamedPackage = (name: string): boolean => {\n\tconst parts = name.split(NAME_SEPARATOR);\n\t// The name has to have 2 parts (without-version), or 3 parts (with version).\n\tif (parts.length < 2 || parts.length > 3) return false;\n\n\tconst [org, app, version] = parts; // split by {org} {app} {optional version}\n\n\t// If the version exists, it must be a number.\n\tif (version !== undefined && !VERSION_REGEX.test(version)) return false;\n\t// Check if the org is a valid SuiNS name.\n\tif (!isValidSuiNSName(org)) return false;\n\n\t// Check if the app is a valid name.\n\treturn NAME_PATTERN.test(app) && app.length < MAX_APP_SIZE;\n};\n\n/**\n * Checks if a type contains valid named packages.\n * This DOES NOT check if the type is a valid Move type.\n */\nexport const isValidNamedType = (type: string): boolean => {\n\t// split our type by all possible type delimeters.\n\tconst splitType = type.split(/::|<|>|,/);\n\tfor (const t of splitType) {\n\t\tif (t.includes(NAME_SEPARATOR) && !isValidNamedPackage(t)) return false;\n\t}\n\t// TODO: Add `isValidStructTag` check once it's introduced.\n\treturn true;\n};\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase58, splitGenericParameters } from '@mysten/bcs';\n\nimport { isValidNamedPackage } from './move-registry.js';\n\nconst TX_DIGEST_LENGTH = 32;\n\n/** Returns whether the tx digest is valid based on the serialization format */\nexport function isValidTransactionDigest(value: string): value is string {\n\ttry {\n\t\tconst buffer = fromBase58(value);\n\t\treturn buffer.length === TX_DIGEST_LENGTH;\n\t} catch (e) {\n\t\treturn false;\n\t}\n}\n\n// TODO - can we automatically sync this with rust length definition?\n// Source of truth is\n// https://github.com/MystenLabs/sui/blob/acb2b97ae21f47600e05b0d28127d88d0725561d/crates/sui-types/src/base_types.rs#L67\n// which uses the Move account address length\n// https://github.com/move-language/move/blob/67ec40dc50c66c34fd73512fcc412f3b68d67235/language/move-core/types/src/account_address.rs#L23 .\n\nexport const SUI_ADDRESS_LENGTH = 32;\nexport function isValidSuiAddress(value: string): value is string {\n\treturn isHex(value) && getHexByteLength(value) === SUI_ADDRESS_LENGTH;\n}\n\nexport function isValidSuiObjectId(value: string): boolean {\n\treturn isValidSuiAddress(value);\n}\n\nexport type StructTag = {\n\taddress: string;\n\tmodule: string;\n\tname: string;\n\ttypeParams: (string | StructTag)[];\n};\n\nfunction parseTypeTag(type: string): string | StructTag {\n\tif (!type.includes('::')) return type;\n\n\treturn parseStructTag(type);\n}\n\nexport function parseStructTag(type: string): StructTag {\n\tconst [address, module] = type.split('::');\n\n\tconst isMvrPackage = isValidNamedPackage(address);\n\n\tconst rest = type.slice(address.length + module.length + 4);\n\tconst name = rest.includes('<') ? rest.slice(0, rest.indexOf('<')) : rest;\n\tconst typeParams = rest.includes('<')\n\t\t? splitGenericParameters(rest.slice(rest.indexOf('<') + 1, rest.lastIndexOf('>'))).map(\n\t\t\t\t(typeParam) => parseTypeTag(typeParam.trim()),\n\t\t\t)\n\t\t: [];\n\n\treturn {\n\t\taddress: isMvrPackage ? address : normalizeSuiAddress(address),\n\t\tmodule,\n\t\tname,\n\t\ttypeParams,\n\t};\n}\n\nexport function normalizeStructTag(type: string | StructTag): string {\n\tconst { address, module, name, typeParams } =\n\t\ttypeof type === 'string' ? parseStructTag(type) : type;\n\n\tconst formattedTypeParams =\n\t\ttypeParams?.length > 0\n\t\t\t? `<${typeParams\n\t\t\t\t\t.map((typeParam) =>\n\t\t\t\t\t\ttypeof typeParam === 'string' ? typeParam : normalizeStructTag(typeParam),\n\t\t\t\t\t)\n\t\t\t\t\t.join(',')}>`\n\t\t\t: '';\n\n\treturn `${address}::${module}::${name}${formattedTypeParams}`;\n}\n\n/**\n * Perform the following operations:\n * 1. Make the address lower case\n * 2. Prepend `0x` if the string does not start with `0x`.\n * 3. Add more zeros if the length of the address(excluding `0x`) is less than `SUI_ADDRESS_LENGTH`\n *\n * WARNING: if the address value itself starts with `0x`, e.g., `0x0x`, the default behavior\n * is to treat the first `0x` not as part of the address. The default behavior can be overridden by\n * setting `forceAdd0x` to true\n *\n */\nexport function normalizeSuiAddress(value: string, forceAdd0x: boolean = false): string {\n\tlet address = value.toLowerCase();\n\tif (!forceAdd0x && address.startsWith('0x')) {\n\t\taddress = address.slice(2);\n\t}\n\treturn `0x${address.padStart(SUI_ADDRESS_LENGTH * 2, '0')}`;\n}\n\nexport function normalizeSuiObjectId(value: string, forceAdd0x: boolean = false): string {\n\treturn normalizeSuiAddress(value, forceAdd0x);\n}\n\nfunction isHex(value: string): boolean {\n\treturn /^(0x|0X)?[a-fA-F0-9]+$/.test(value) && value.length % 2 === 0;\n}\n\nfunction getHexByteLength(value: string): number {\n\treturn /^(0x|0X)/.test(value) ? (value.length - 2) / 2 : value.length / 2;\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { normalizeSuiObjectId } from './sui-types.js';\n\nexport const SUI_DECIMALS = 9;\nexport const MIST_PER_SUI = BigInt(**********);\n\nexport const MOVE_STDLIB_ADDRESS = '0x1';\nexport const SUI_FRAMEWORK_ADDRESS = '0x2';\nexport const SUI_SYSTEM_ADDRESS = '0x3';\nexport const SUI_CLOCK_OBJECT_ID = normalizeSuiObjectId('0x6');\nexport const SUI_SYSTEM_MODULE_NAME = 'sui_system';\nexport const SUI_TYPE_ARG = `${SUI_FRAMEWORK_ADDRESS}::sui::SUI`;\nexport const SUI_SYSTEM_STATE_OBJECT_ID: string = normalizeSuiObjectId('0x5');\n", "/**\n * Internal webcrypto alias.\n * We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n * See utils.ts for details.\n * @module\n */\ndeclare const globalThis: Record<string, any> | undefined;\nexport const crypto: any =\n  typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;\n", "/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\n\n/** Checks if something is Uint8Array. Be careful: nodejs <PERSON>uffer will return true. */\nexport function isBytes(a: unknown): a is Uint8Array {\n  return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n\n/** Asserts something is positive integer. */\nexport function anumber(n: number): void {\n  if (!Number.isSafeInteger(n) || n < 0) throw new Error('positive integer expected, got ' + n);\n}\n\n/** Asserts something is Uint8Array. */\nexport function abytes(b: Uint8Array | undefined, ...lengths: number[]): void {\n  if (!isBytes(b)) throw new Error('Uint8Array expected');\n  if (lengths.length > 0 && !lengths.includes(b.length))\n    throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n\n/** Asserts something is hash */\nexport function ahash(h: IHash): void {\n  if (typeof h !== 'function' || typeof h.create !== 'function')\n    throw new Error('Hash should be wrapped by utils.createHasher');\n  anumber(h.outputLen);\n  anumber(h.blockLen);\n}\n\n/** Asserts a hash instance has not been destroyed / finished */\nexport function aexists(instance: any, checkFinished = true): void {\n  if (instance.destroyed) throw new Error('Hash instance has been destroyed');\n  if (checkFinished && instance.finished) throw new Error('Hash#digest() has already been called');\n}\n\n/** Asserts output is properly-sized byte array */\nexport function aoutput(out: any, instance: any): void {\n  abytes(out);\n  const min = instance.outputLen;\n  if (out.length < min) {\n    throw new Error('digestInto() expects output buffer of length at least ' + min);\n  }\n}\n\n/** Generic type encompassing 8/16/32-byte arrays - but not 64-byte. */\n// prettier-ignore\nexport type TypedArray = Int8Array | Uint8ClampedArray | Uint8Array |\n  Uint16Array | Int16Array | Uint32Array | Int32Array;\n\n/** Cast u8 / u16 / u32 to u8. */\nexport function u8(arr: TypedArray): Uint8Array {\n  return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n\n/** Cast u8 / u16 / u32 to u32. */\nexport function u32(arr: TypedArray): Uint32Array {\n  return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nexport function clean(...arrays: TypedArray[]): void {\n  for (let i = 0; i < arrays.length; i++) {\n    arrays[i].fill(0);\n  }\n}\n\n/** Create DataView of an array for easy byte-level manipulation. */\nexport function createView(arr: TypedArray): DataView {\n  return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n\n/** The rotate right (circular right shift) operation for uint32 */\nexport function rotr(word: number, shift: number): number {\n  return (word << (32 - shift)) | (word >>> shift);\n}\n\n/** The rotate left (circular left shift) operation for uint32 */\nexport function rotl(word: number, shift: number): number {\n  return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nexport const isLE: boolean = /* @__PURE__ */ (() =>\n  new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n\n/** The byte swap operation for uint32 */\nexport function byteSwap(word: number): number {\n  return (\n    ((word << 24) & 0xff000000) |\n    ((word << 8) & 0xff0000) |\n    ((word >>> 8) & 0xff00) |\n    ((word >>> 24) & 0xff)\n  );\n}\n/** Conditionally byte swap if on a big-endian platform */\nexport const swap8IfBE: (n: number) => number = isLE\n  ? (n: number) => n\n  : (n: number) => byteSwap(n);\n\n/** @deprecated */\nexport const byteSwapIfBE: typeof swap8IfBE = swap8IfBE;\n/** In place byte swap for Uint32Array */\nexport function byteSwap32(arr: Uint32Array): Uint32Array {\n  for (let i = 0; i < arr.length; i++) {\n    arr[i] = byteSwap(arr[i]);\n  }\n  return arr;\n}\n\nexport const swap32IfBE: (u: Uint32Array) => Uint32Array = isLE\n  ? (u: Uint32Array) => u\n  : byteSwap32;\n\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin: boolean = /* @__PURE__ */ (() =>\n  // @ts-ignore\n  typeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) =>\n  i.toString(16).padStart(2, '0')\n);\n\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes: Uint8Array): string {\n  abytes(bytes);\n  // @ts-ignore\n  if (hasHexBuiltin) return bytes.toHex();\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 } as const;\nfunction asciiToBase16(ch: number): number | undefined {\n  if (ch >= asciis._0 && ch <= asciis._9) return ch - asciis._0; // '2' => 50-48\n  if (ch >= asciis.A && ch <= asciis.F) return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n  if (ch >= asciis.a && ch <= asciis.f) return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n  return;\n}\n\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex: string): Uint8Array {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  // @ts-ignore\n  if (hasHexBuiltin) return Uint8Array.fromHex(hex);\n  const hl = hex.length;\n  const al = hl / 2;\n  if (hl % 2) throw new Error('hex string expected, got unpadded hex of length ' + hl);\n  const array = new Uint8Array(al);\n  for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n    const n1 = asciiToBase16(hex.charCodeAt(hi));\n    const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n    if (n1 === undefined || n2 === undefined) {\n      const char = hex[hi] + hex[hi + 1];\n      throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n    }\n    array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n  }\n  return array;\n}\n\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nexport const nextTick = async (): Promise<void> => {};\n\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nexport async function asyncLoop(\n  iters: number,\n  tick: number,\n  cb: (i: number) => void\n): Promise<void> {\n  let ts = Date.now();\n  for (let i = 0; i < iters; i++) {\n    cb(i);\n    // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n    const diff = Date.now() - ts;\n    if (diff >= 0 && diff < tick) continue;\n    await nextTick();\n    ts += diff;\n  }\n}\n\n// Global symbols, but ts doesn't see them: https://github.com/microsoft/TypeScript/issues/31535\ndeclare const TextEncoder: any;\ndeclare const TextDecoder: any;\n\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nexport function utf8ToBytes(str: string): Uint8Array {\n  if (typeof str !== 'string') throw new Error('string expected');\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nexport function bytesToUtf8(bytes: Uint8Array): string {\n  return new TextDecoder().decode(bytes);\n}\n\n/** Accepted input of hash functions. Strings are converted to byte arrays. */\nexport type Input = string | Uint8Array;\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data: Input): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n\n/** KDFs can accept string or Uint8Array for user convenience. */\nexport type KDFInput = string | Uint8Array;\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nexport function kdfInputToBytes(data: KDFInput): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n\n/** Copies several Uint8Arrays into one. */\nexport function concatBytes(...arrays: Uint8Array[]): Uint8Array {\n  let sum = 0;\n  for (let i = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    abytes(a);\n    sum += a.length;\n  }\n  const res = new Uint8Array(sum);\n  for (let i = 0, pad = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    res.set(a, pad);\n    pad += a.length;\n  }\n  return res;\n}\n\ntype EmptyObj = {};\nexport function checkOpts<T1 extends EmptyObj, T2 extends EmptyObj>(\n  defaults: T1,\n  opts?: T2\n): T1 & T2 {\n  if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n    throw new Error('options should be object or undefined');\n  const merged = Object.assign(defaults, opts);\n  return merged as T1 & T2;\n}\n\n/** Hash interface. */\nexport type IHash = {\n  (data: Uint8Array): Uint8Array;\n  blockLen: number;\n  outputLen: number;\n  create: any;\n};\n\n/** For runtime check if class implements interface */\nexport abstract class Hash<T extends Hash<T>> {\n  abstract blockLen: number; // Bytes per block\n  abstract outputLen: number; // Bytes in output\n  abstract update(buf: Input): this;\n  // Writes digest into buf\n  abstract digestInto(buf: Uint8Array): void;\n  abstract digest(): Uint8Array;\n  /**\n   * Resets internal state. Makes Hash instance unusable.\n   * Reset is impossible for keyed hashes if key is consumed into state. If digest is not consumed\n   * by user, they will need to manually call `destroy()` when zeroing is necessary.\n   */\n  abstract destroy(): void;\n  /**\n   * Clones hash instance. Unsafe: doesn't check whether `to` is valid. Can be used as `clone()`\n   * when no options are passed.\n   * Reasons to use `_cloneInto` instead of clone: 1) performance 2) reuse instance => all internal\n   * buffers are overwritten => causes buffer overwrite which is used for digest in some cases.\n   * There are no guarantees for clean-up because it's impossible in JS.\n   */\n  abstract _cloneInto(to?: T): T;\n  // Safe version that clones internal state\n  abstract clone(): T;\n}\n\n/**\n * XOF: streaming API to read digest in chunks.\n * Same as 'squeeze' in keccak/k12 and 'seek' in blake3, but more generic name.\n * When hash used in XOF mode it is up to user to call '.destroy' afterwards, since we cannot\n * destroy state, next call can require more bytes.\n */\nexport type HashXOF<T extends Hash<T>> = Hash<T> & {\n  xof(bytes: number): Uint8Array; // Read 'bytes' bytes from digest stream\n  xofInto(buf: Uint8Array): Uint8Array; // read buf.length bytes from digest stream into buf\n};\n\n/** Hash function */\nexport type CHash = ReturnType<typeof createHasher>;\n/** Hash function with output */\nexport type CHashO = ReturnType<typeof createOptHasher>;\n/** XOF with output */\nexport type CHashXO = ReturnType<typeof createXOFer>;\n\n/** Wraps hash function, creating an interface on top of it */\nexport function createHasher<T extends Hash<T>>(\n  hashCons: () => Hash<T>\n): {\n  (msg: Input): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(): Hash<T>;\n} {\n  const hashC = (msg: Input): Uint8Array => hashCons().update(toBytes(msg)).digest();\n  const tmp = hashCons();\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = () => hashCons();\n  return hashC;\n}\n\nexport function createOptHasher<H extends Hash<H>, T extends Object>(\n  hashCons: (opts?: T) => Hash<H>\n): {\n  (msg: Input, opts?: T): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(opts?: T): Hash<H>;\n} {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts?: T) => hashCons(opts);\n  return hashC;\n}\n\nexport function createXOFer<H extends HashXOF<H>, T extends Object>(\n  hashCons: (opts?: T) => HashXOF<H>\n): {\n  (msg: Input, opts?: T): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(opts?: T): HashXOF<H>;\n} {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts?: T) => hashCons(opts);\n  return hashC;\n}\nexport const wrapConstructor: typeof createHasher = createHasher;\nexport const wrapConstructorWithOpts: typeof createOptHasher = createOptHasher;\nexport const wrapXOFConstructorWithOpts: typeof createXOFer = createXOFer;\n\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nexport function randomBytes(bytesLength = 32): Uint8Array {\n  if (crypto && typeof crypto.getRandomValues === 'function') {\n    return crypto.getRandomValues(new Uint8Array(bytesLength));\n  }\n  // Legacy Node.js compatibility\n  if (crypto && typeof crypto.randomBytes === 'function') {\n    return Uint8Array.from(crypto.randomBytes(bytesLength));\n  }\n  throw new Error('crypto.getRandomValues must be defined');\n}\n", "/**\n * Internal helpers for blake hash.\n * @module\n */\nimport { rotr } from './utils.ts';\n\n/**\n * Internal blake variable.\n * For BLAKE2b, the two extra permutations for rounds 10 and 11 are SIGMA[10..11] = SIGMA[0..1].\n */\n// prettier-ignore\nexport const BSIGMA: Uint8Array = /* @__PURE__ */ Uint8Array.from([\n  0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,\n  14, 10, 4, 8, 9, 15, 13, 6, 1, 12, 0, 2, 11, 7, 5, 3,\n  11, 8, 12, 0, 5, 2, 15, 13, 10, 14, 3, 6, 7, 1, 9, 4,\n  7, 9, 3, 1, 13, 12, 11, 14, 2, 6, 5, 10, 4, 0, 15, 8,\n  9, 0, 5, 7, 2, 4, 10, 15, 14, 1, 11, 12, 6, 8, 3, 13,\n  2, 12, 6, 10, 0, 11, 8, 3, 4, 13, 7, 5, 15, 14, 1, 9,\n  12, 5, 1, 15, 14, 13, 4, 10, 0, 7, 6, 3, 9, 2, 8, 11,\n  13, 11, 7, 14, 12, 1, 3, 9, 5, 0, 15, 4, 8, 6, 2, 10,\n  6, 15, 14, 9, 11, 3, 0, 8, 12, 2, 13, 7, 1, 4, 10, 5,\n  10, 2, 8, 4, 7, 6, 1, 5, 15, 11, 9, 14, 3, 12, 13, 0,\n  0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,\n  14, 10, 4, 8, 9, 15, 13, 6, 1, 12, 0, 2, 11, 7, 5, 3,\n  // Blake1, unused in others\n  11, 8, 12, 0, 5, 2, 15, 13, 10, 14, 3, 6, 7, 1, 9, 4,\n  7, 9, 3, 1, 13, 12, 11, 14, 2, 6, 5, 10, 4, 0, 15, 8,\n  9, 0, 5, 7, 2, 4, 10, 15, 14, 1, 11, 12, 6, 8, 3, 13,\n  2, 12, 6, 10, 0, 11, 8, 3, 4, 13, 7, 5, 15, 14, 1, 9,\n]);\n\n// prettier-ignore\nexport type Num4 = { a: number; b: number; c: number; d: number; };\n\n// Mixing function G splitted in two halfs\nexport function G1s(a: number, b: number, c: number, d: number, x: number): Num4 {\n  a = (a + b + x) | 0;\n  d = rotr(d ^ a, 16);\n  c = (c + d) | 0;\n  b = rotr(b ^ c, 12);\n  return { a, b, c, d };\n}\n\nexport function G2s(a: number, b: number, c: number, d: number, x: number): Num4 {\n  a = (a + b + x) | 0;\n  d = rotr(d ^ a, 8);\n  c = (c + d) | 0;\n  b = rotr(b ^ c, 7);\n  return { a, b, c, d };\n}\n", "/**\n * Internal Merkle-<PERSON>gard hash utils.\n * @module\n */\nimport { type Input, Hash, abytes, aexists, aoutput, clean, createView, toBytes } from './utils.ts';\n\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nexport function setBigUint64(\n  view: DataView,\n  byteOffset: number,\n  value: bigint,\n  isLE: boolean\n): void {\n  if (typeof view.setBigUint64 === 'function') return view.setBigUint64(byteOffset, value, isLE);\n  const _32n = BigInt(32);\n  const _u32_max = BigInt(0xffffffff);\n  const wh = Number((value >> _32n) & _u32_max);\n  const wl = Number(value & _u32_max);\n  const h = isLE ? 4 : 0;\n  const l = isLE ? 0 : 4;\n  view.setUint32(byteOffset + h, wh, isLE);\n  view.setUint32(byteOffset + l, wl, isLE);\n}\n\n/** Choice: a ? b : c */\nexport function Chi(a: number, b: number, c: number): number {\n  return (a & b) ^ (~a & c);\n}\n\n/** Majority function, true if any two inputs is true. */\nexport function Maj(a: number, b: number, c: number): number {\n  return (a & b) ^ (a & c) ^ (b & c);\n}\n\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nexport abstract class HashMD<T extends HashMD<T>> extends Hash<T> {\n  protected abstract process(buf: DataView, offset: number): void;\n  protected abstract get(): number[];\n  protected abstract set(...args: number[]): void;\n  abstract destroy(): void;\n  protected abstract roundClean(): void;\n\n  readonly blockLen: number;\n  readonly outputLen: number;\n  readonly padOffset: number;\n  readonly isLE: boolean;\n\n  // For partial updates less than block size\n  protected buffer: Uint8Array;\n  protected view: DataView;\n  protected finished = false;\n  protected length = 0;\n  protected pos = 0;\n  protected destroyed = false;\n\n  constructor(blockLen: number, outputLen: number, padOffset: number, isLE: boolean) {\n    super();\n    this.blockLen = blockLen;\n    this.outputLen = outputLen;\n    this.padOffset = padOffset;\n    this.isLE = isLE;\n    this.buffer = new Uint8Array(blockLen);\n    this.view = createView(this.buffer);\n  }\n  update(data: Input): this {\n    aexists(this);\n    data = toBytes(data);\n    abytes(data);\n    const { view, buffer, blockLen } = this;\n    const len = data.length;\n    for (let pos = 0; pos < len; ) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      // Fast path: we have at least one block in input, cast it to view and process\n      if (take === blockLen) {\n        const dataView = createView(data);\n        for (; blockLen <= len - pos; pos += blockLen) this.process(dataView, pos);\n        continue;\n      }\n      buffer.set(data.subarray(pos, pos + take), this.pos);\n      this.pos += take;\n      pos += take;\n      if (this.pos === blockLen) {\n        this.process(view, 0);\n        this.pos = 0;\n      }\n    }\n    this.length += data.length;\n    this.roundClean();\n    return this;\n  }\n  digestInto(out: Uint8Array): void {\n    aexists(this);\n    aoutput(out, this);\n    this.finished = true;\n    // Padding\n    // We can avoid allocation of buffer for padding completely if it\n    // was previously not allocated here. But it won't change performance.\n    const { buffer, view, blockLen, isLE } = this;\n    let { pos } = this;\n    // append the bit '1' to the message\n    buffer[pos++] = 0b10000000;\n    clean(this.buffer.subarray(pos));\n    // we have less than padOffset left in buffer, so we cannot put length in\n    // current block, need process it and pad again\n    if (this.padOffset > blockLen - pos) {\n      this.process(view, 0);\n      pos = 0;\n    }\n    // Pad until full block byte with zeros\n    for (let i = pos; i < blockLen; i++) buffer[i] = 0;\n    // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n    // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n    // So we just write lowest 64 bits of that value.\n    setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n    this.process(view, 0);\n    const oview = createView(out);\n    const len = this.outputLen;\n    // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n    if (len % 4) throw new Error('_sha2: outputLen should be aligned to 32bit');\n    const outLen = len / 4;\n    const state = this.get();\n    if (outLen > state.length) throw new Error('_sha2: outputLen bigger than state');\n    for (let i = 0; i < outLen; i++) oview.setUint32(4 * i, state[i], isLE);\n  }\n  digest(): Uint8Array {\n    const { buffer, outputLen } = this;\n    this.digestInto(buffer);\n    const res = buffer.slice(0, outputLen);\n    this.destroy();\n    return res;\n  }\n  _cloneInto(to?: T): T {\n    to ||= new (this.constructor as any)() as T;\n    to.set(...this.get());\n    const { blockLen, buffer, length, finished, destroyed, pos } = this;\n    to.destroyed = destroyed;\n    to.finished = finished;\n    to.length = length;\n    to.pos = pos;\n    if (length % blockLen) to.buffer.set(buffer);\n    return to;\n  }\n  clone(): T {\n    return this._cloneInto();\n  }\n}\n\n/**\n * Initial SHA-2 state: fractional parts of square roots of first 16 primes 2..53.\n * Check out `test/misc/sha2-gen-iv.js` for recomputation guide.\n */\n\n/** Initial SHA256 state. Bits 0..32 of frac part of sqrt of primes 2..19 */\nexport const SHA256_IV: Uint32Array = /* @__PURE__ */ Uint32Array.from([\n  0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19,\n]);\n\n/** Initial SHA224 state. Bits 32..64 of frac part of sqrt of primes 23..53 */\nexport const SHA224_IV: Uint32Array = /* @__PURE__ */ Uint32Array.from([\n  0xc1059ed8, 0x367cd507, 0x3070dd17, 0xf70e5939, 0xffc00b31, 0x68581511, 0x64f98fa7, 0xbefa4fa4,\n]);\n\n/** Initial SHA384 state. Bits 0..64 of frac part of sqrt of primes 23..53 */\nexport const SHA384_IV: Uint32Array = /* @__PURE__ */ Uint32Array.from([\n  0xcbbb9d5d, 0xc1059ed8, 0x629a292a, 0x367cd507, 0x9159015a, 0x3070dd17, 0x152fecd8, 0xf70e5939,\n  0x67332667, 0xffc00b31, 0x8eb44a87, 0x68581511, 0xdb0c2e0d, 0x64f98fa7, 0x47b5481d, 0xbefa4fa4,\n]);\n\n/** Initial SHA512 state. Bits 0..64 of frac part of sqrt of primes 2..19 */\nexport const SHA512_IV: Uint32Array = /* @__PURE__ */ Uint32Array.from([\n  0x6a09e667, 0xf3bcc908, 0xbb67ae85, 0x84caa73b, 0x3c6ef372, 0xfe94f82b, 0xa54ff53a, 0x5f1d36f1,\n  0x510e527f, 0xade682d1, 0x9b05688c, 0x2b3e6c1f, 0x1f83d9ab, 0xfb41bd6b, 0x5be0cd19, 0x137e2179,\n]);\n", "/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n\nfunction fromBig(\n  n: bigint,\n  le = false\n): {\n  h: number;\n  l: number;\n} {\n  if (le) return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n  return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\n\nfunction split(lst: bigint[], le = false): Uint32Array[] {\n  const len = lst.length;\n  let Ah = new Uint32Array(len);\n  let Al = new Uint32Array(len);\n  for (let i = 0; i < len; i++) {\n    const { h, l } = fromBig(lst[i], le);\n    [Ah[i], Al[i]] = [h, l];\n  }\n  return [Ah, Al];\n}\n\nconst toBig = (h: number, l: number): bigint => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h: number, _l: number, s: number): number => h >>> s;\nconst shrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h: number, l: number, s: number): number => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h: number, l: number, s: number): number => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h: number, l: number, s: number): number => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h: number, l: number): number => l;\nconst rotr32L = (h: number, _l: number): number => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h: number, l: number, s: number): number => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h: number, l: number, s: number): number => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h: number, l: number, s: number): number => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h: number, l: number, s: number): number => (h << (s - 32)) | (l >>> (64 - s));\n\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(\n  Ah: number,\n  Al: number,\n  Bh: number,\n  Bl: number\n): {\n  h: number;\n  l: number;\n} {\n  const l = (Al >>> 0) + (Bl >>> 0);\n  return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al: number, Bl: number, Cl: number): number => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low: number, Ah: number, Bh: number, Ch: number): number =>\n  (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al: number, Bl: number, Cl: number, Dl: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number): number =>\n  (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al: number, Bl: number, Cl: number, Dl: number, El: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number): number =>\n  (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n\n// prettier-ignore\nexport {\n  add, add3H, add3L, add4H, add4L, add5H, add5L, fromBig, rotlBH, rotlBL, rotlSH, rotlSL, rotr32H, rotr32L, rotrBH, rotrBL, rotrSH, rotrSL, shrSH, shrSL, split, toBig\n};\n// prettier-ignore\nconst u64: { fromBig: typeof fromBig; split: typeof split; toBig: (h: number, l: number) => bigint; shrSH: (h: number, _l: number, s: number) => number; shrSL: (h: number, l: number, s: number) => number; rotrSH: (h: number, l: number, s: number) => number; rotrSL: (h: number, l: number, s: number) => number; rotrBH: (h: number, l: number, s: number) => number; rotrBL: (h: number, l: number, s: number) => number; rotr32H: (_h: number, l: number) => number; rotr32L: (h: number, _l: number) => number; rotlSH: (h: number, l: number, s: number) => number; rotlSL: (h: number, l: number, s: number) => number; rotlBH: (h: number, l: number, s: number) => number; rotlBL: (h: number, l: number, s: number) => number; add: typeof add; add3L: (Al: number, Bl: number, Cl: number) => number; add3H: (low: number, Ah: number, Bh: number, Ch: number) => number; add4L: (Al: number, Bl: number, Cl: number, Dl: number) => number; add4H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number) => number; add5H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number) => number; add5L: (Al: number, Bl: number, Cl: number, Dl: number, El: number) => number; } = {\n  fromBig, split, toBig,\n  shrSH, shrSL,\n  rotrSH, rotrSL, rotrBH, rotrBL,\n  rotr32H, rotr32L,\n  rotlSH, rotlSL, rotlBH, rotlBL,\n  add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n", "/**\n * blake2b (64-bit) & blake2s (8 to 32-bit) hash functions.\n * b could have been faster, but there is no fast u64 in js, so s is 1.5x faster.\n * @module\n */\nimport { BSIGMA, G1s, G2s } from './_blake.ts';\nimport { SHA256_IV } from './_md.ts';\nimport * as u64 from './_u64.ts';\n// prettier-ignore\nimport {\n  abytes, aexists, anumber, aoutput,\n  clean, createOptHasher, Hash, swap32IfBE, swap8IfBE, toBytes, u32,\n  type CHashO, type Input\n} from './utils.ts';\n\n/** Blake hash options. dkLen is output length. key is used in MAC mode. salt is used in KDF mode. */\nexport type Blake2Opts = {\n  dkLen?: number;\n  key?: Input;\n  salt?: Input;\n  personalization?: Input;\n};\n\n// Same as SHA512_IV, but swapped endianness: LE instead of BE. iv[1] is iv[0], etc.\nconst B2B_IV = /* @__PURE__ */ Uint32Array.from([\n  0xf3bcc908, 0x6a09e667, 0x84caa73b, 0xbb67ae85, 0xfe94f82b, 0x3c6ef372, 0x5f1d36f1, 0xa54ff53a,\n  0xade682d1, 0x510e527f, 0x2b3e6c1f, 0x9b05688c, 0xfb41bd6b, 0x1f83d9ab, 0x137e2179, 0x5be0cd19,\n]);\n// Temporary buffer\nconst BBUF = /* @__PURE__ */ new Uint32Array(32);\n\n// Mixing function G splitted in two halfs\nfunction G1b(a: number, b: number, c: number, d: number, msg: Uint32Array, x: number) {\n  // NOTE: V is LE here\n  const Xl = msg[x], Xh = msg[x + 1]; // prettier-ignore\n  let Al = BBUF[2 * a], Ah = BBUF[2 * a + 1]; // prettier-ignore\n  let Bl = BBUF[2 * b], Bh = BBUF[2 * b + 1]; // prettier-ignore\n  let Cl = BBUF[2 * c], Ch = BBUF[2 * c + 1]; // prettier-ignore\n  let Dl = BBUF[2 * d], Dh = BBUF[2 * d + 1]; // prettier-ignore\n  // v[a] = (v[a] + v[b] + x) | 0;\n  let ll = u64.add3L(Al, Bl, Xl);\n  Ah = u64.add3H(ll, Ah, Bh, Xh);\n  Al = ll | 0;\n  // v[d] = rotr(v[d] ^ v[a], 32)\n  ({ Dh, Dl } = { Dh: Dh ^ Ah, Dl: Dl ^ Al });\n  ({ Dh, Dl } = { Dh: u64.rotr32H(Dh, Dl), Dl: u64.rotr32L(Dh, Dl) });\n  // v[c] = (v[c] + v[d]) | 0;\n  ({ h: Ch, l: Cl } = u64.add(Ch, Cl, Dh, Dl));\n  // v[b] = rotr(v[b] ^ v[c], 24)\n  ({ Bh, Bl } = { Bh: Bh ^ Ch, Bl: Bl ^ Cl });\n  ({ Bh, Bl } = { Bh: u64.rotrSH(Bh, Bl, 24), Bl: u64.rotrSL(Bh, Bl, 24) });\n  (BBUF[2 * a] = Al), (BBUF[2 * a + 1] = Ah);\n  (BBUF[2 * b] = Bl), (BBUF[2 * b + 1] = Bh);\n  (BBUF[2 * c] = Cl), (BBUF[2 * c + 1] = Ch);\n  (BBUF[2 * d] = Dl), (BBUF[2 * d + 1] = Dh);\n}\n\nfunction G2b(a: number, b: number, c: number, d: number, msg: Uint32Array, x: number) {\n  // NOTE: V is LE here\n  const Xl = msg[x], Xh = msg[x + 1]; // prettier-ignore\n  let Al = BBUF[2 * a], Ah = BBUF[2 * a + 1]; // prettier-ignore\n  let Bl = BBUF[2 * b], Bh = BBUF[2 * b + 1]; // prettier-ignore\n  let Cl = BBUF[2 * c], Ch = BBUF[2 * c + 1]; // prettier-ignore\n  let Dl = BBUF[2 * d], Dh = BBUF[2 * d + 1]; // prettier-ignore\n  // v[a] = (v[a] + v[b] + x) | 0;\n  let ll = u64.add3L(Al, Bl, Xl);\n  Ah = u64.add3H(ll, Ah, Bh, Xh);\n  Al = ll | 0;\n  // v[d] = rotr(v[d] ^ v[a], 16)\n  ({ Dh, Dl } = { Dh: Dh ^ Ah, Dl: Dl ^ Al });\n  ({ Dh, Dl } = { Dh: u64.rotrSH(Dh, Dl, 16), Dl: u64.rotrSL(Dh, Dl, 16) });\n  // v[c] = (v[c] + v[d]) | 0;\n  ({ h: Ch, l: Cl } = u64.add(Ch, Cl, Dh, Dl));\n  // v[b] = rotr(v[b] ^ v[c], 63)\n  ({ Bh, Bl } = { Bh: Bh ^ Ch, Bl: Bl ^ Cl });\n  ({ Bh, Bl } = { Bh: u64.rotrBH(Bh, Bl, 63), Bl: u64.rotrBL(Bh, Bl, 63) });\n  (BBUF[2 * a] = Al), (BBUF[2 * a + 1] = Ah);\n  (BBUF[2 * b] = Bl), (BBUF[2 * b + 1] = Bh);\n  (BBUF[2 * c] = Cl), (BBUF[2 * c + 1] = Ch);\n  (BBUF[2 * d] = Dl), (BBUF[2 * d + 1] = Dh);\n}\n\nfunction checkBlake2Opts(\n  outputLen: number,\n  opts: Blake2Opts | undefined = {},\n  keyLen: number,\n  saltLen: number,\n  persLen: number\n) {\n  anumber(keyLen);\n  if (outputLen < 0 || outputLen > keyLen) throw new Error('outputLen bigger than keyLen');\n  const { key, salt, personalization } = opts;\n  if (key !== undefined && (key.length < 1 || key.length > keyLen))\n    throw new Error('key length must be undefined or 1..' + keyLen);\n  if (salt !== undefined && salt.length !== saltLen)\n    throw new Error('salt must be undefined or ' + saltLen);\n  if (personalization !== undefined && personalization.length !== persLen)\n    throw new Error('personalization must be undefined or ' + persLen);\n}\n\n/** Class, from which others are subclassed. */\nexport abstract class BLAKE2<T extends BLAKE2<T>> extends Hash<T> {\n  protected abstract compress(msg: Uint32Array, offset: number, isLast: boolean): void;\n  protected abstract get(): number[];\n  protected abstract set(...args: number[]): void;\n  abstract destroy(): void;\n  protected buffer: Uint8Array;\n  protected buffer32: Uint32Array;\n  protected finished = false;\n  protected destroyed = false;\n  protected length: number = 0;\n  protected pos: number = 0;\n  readonly blockLen: number;\n  readonly outputLen: number;\n\n  constructor(blockLen: number, outputLen: number) {\n    super();\n    anumber(blockLen);\n    anumber(outputLen);\n    this.blockLen = blockLen;\n    this.outputLen = outputLen;\n    this.buffer = new Uint8Array(blockLen);\n    this.buffer32 = u32(this.buffer);\n  }\n  update(data: Input): this {\n    aexists(this);\n    data = toBytes(data);\n    abytes(data);\n    // Main difference with other hashes: there is flag for last block,\n    // so we cannot process current block before we know that there\n    // is the next one. This significantly complicates logic and reduces ability\n    // to do zero-copy processing\n    const { blockLen, buffer, buffer32 } = this;\n    const len = data.length;\n    const offset = data.byteOffset;\n    const buf = data.buffer;\n    for (let pos = 0; pos < len; ) {\n      // If buffer is full and we still have input (don't process last block, same as blake2s)\n      if (this.pos === blockLen) {\n        swap32IfBE(buffer32);\n        this.compress(buffer32, 0, false);\n        swap32IfBE(buffer32);\n        this.pos = 0;\n      }\n      const take = Math.min(blockLen - this.pos, len - pos);\n      const dataOffset = offset + pos;\n      // full block && aligned to 4 bytes && not last in input\n      if (take === blockLen && !(dataOffset % 4) && pos + take < len) {\n        const data32 = new Uint32Array(buf, dataOffset, Math.floor((len - pos) / 4));\n        swap32IfBE(data32);\n        for (let pos32 = 0; pos + blockLen < len; pos32 += buffer32.length, pos += blockLen) {\n          this.length += blockLen;\n          this.compress(data32, pos32, false);\n        }\n        swap32IfBE(data32);\n        continue;\n      }\n      buffer.set(data.subarray(pos, pos + take), this.pos);\n      this.pos += take;\n      this.length += take;\n      pos += take;\n    }\n    return this;\n  }\n  digestInto(out: Uint8Array): void {\n    aexists(this);\n    aoutput(out, this);\n    const { pos, buffer32 } = this;\n    this.finished = true;\n    // Padding\n    clean(this.buffer.subarray(pos));\n    swap32IfBE(buffer32);\n    this.compress(buffer32, 0, true);\n    swap32IfBE(buffer32);\n    const out32 = u32(out);\n    this.get().forEach((v, i) => (out32[i] = swap8IfBE(v)));\n  }\n  digest(): Uint8Array {\n    const { buffer, outputLen } = this;\n    this.digestInto(buffer);\n    const res = buffer.slice(0, outputLen);\n    this.destroy();\n    return res;\n  }\n  _cloneInto(to?: T): T {\n    const { buffer, length, finished, destroyed, outputLen, pos } = this;\n    to ||= new (this.constructor as any)({ dkLen: outputLen }) as T;\n    to.set(...this.get());\n    to.buffer.set(buffer);\n    to.destroyed = destroyed;\n    to.finished = finished;\n    to.length = length;\n    to.pos = pos;\n    // @ts-ignore\n    to.outputLen = outputLen;\n    return to;\n  }\n  clone(): T {\n    return this._cloneInto();\n  }\n}\n\nexport class BLAKE2b extends BLAKE2<BLAKE2b> {\n  // Same as SHA-512, but LE\n  private v0l = B2B_IV[0] | 0;\n  private v0h = B2B_IV[1] | 0;\n  private v1l = B2B_IV[2] | 0;\n  private v1h = B2B_IV[3] | 0;\n  private v2l = B2B_IV[4] | 0;\n  private v2h = B2B_IV[5] | 0;\n  private v3l = B2B_IV[6] | 0;\n  private v3h = B2B_IV[7] | 0;\n  private v4l = B2B_IV[8] | 0;\n  private v4h = B2B_IV[9] | 0;\n  private v5l = B2B_IV[10] | 0;\n  private v5h = B2B_IV[11] | 0;\n  private v6l = B2B_IV[12] | 0;\n  private v6h = B2B_IV[13] | 0;\n  private v7l = B2B_IV[14] | 0;\n  private v7h = B2B_IV[15] | 0;\n\n  constructor(opts: Blake2Opts = {}) {\n    const olen = opts.dkLen === undefined ? 64 : opts.dkLen;\n    super(128, olen);\n    checkBlake2Opts(olen, opts, 64, 16, 16);\n    let { key, personalization, salt } = opts;\n    let keyLength = 0;\n    if (key !== undefined) {\n      key = toBytes(key);\n      keyLength = key.length;\n    }\n    this.v0l ^= this.outputLen | (keyLength << 8) | (0x01 << 16) | (0x01 << 24);\n    if (salt !== undefined) {\n      salt = toBytes(salt);\n      const slt = u32(salt);\n      this.v4l ^= swap8IfBE(slt[0]);\n      this.v4h ^= swap8IfBE(slt[1]);\n      this.v5l ^= swap8IfBE(slt[2]);\n      this.v5h ^= swap8IfBE(slt[3]);\n    }\n    if (personalization !== undefined) {\n      personalization = toBytes(personalization);\n      const pers = u32(personalization);\n      this.v6l ^= swap8IfBE(pers[0]);\n      this.v6h ^= swap8IfBE(pers[1]);\n      this.v7l ^= swap8IfBE(pers[2]);\n      this.v7h ^= swap8IfBE(pers[3]);\n    }\n    if (key !== undefined) {\n      // Pad to blockLen and update\n      const tmp = new Uint8Array(this.blockLen);\n      tmp.set(key);\n      this.update(tmp);\n    }\n  }\n  // prettier-ignore\n  protected get(): [\n    number, number, number, number, number, number, number, number,\n    number, number, number, number, number, number, number, number\n  ] {\n    let { v0l, v0h, v1l, v1h, v2l, v2h, v3l, v3h, v4l, v4h, v5l, v5h, v6l, v6h, v7l, v7h } = this;\n    return [v0l, v0h, v1l, v1h, v2l, v2h, v3l, v3h, v4l, v4h, v5l, v5h, v6l, v6h, v7l, v7h];\n  }\n  // prettier-ignore\n  protected set(\n    v0l: number, v0h: number, v1l: number, v1h: number,\n    v2l: number, v2h: number, v3l: number, v3h: number,\n    v4l: number, v4h: number, v5l: number, v5h: number,\n    v6l: number, v6h: number, v7l: number, v7h: number\n  ): void {\n    this.v0l = v0l | 0;\n    this.v0h = v0h | 0;\n    this.v1l = v1l | 0;\n    this.v1h = v1h | 0;\n    this.v2l = v2l | 0;\n    this.v2h = v2h | 0;\n    this.v3l = v3l | 0;\n    this.v3h = v3h | 0;\n    this.v4l = v4l | 0;\n    this.v4h = v4h | 0;\n    this.v5l = v5l | 0;\n    this.v5h = v5h | 0;\n    this.v6l = v6l | 0;\n    this.v6h = v6h | 0;\n    this.v7l = v7l | 0;\n    this.v7h = v7h | 0;\n  }\n  protected compress(msg: Uint32Array, offset: number, isLast: boolean): void {\n    this.get().forEach((v, i) => (BBUF[i] = v)); // First half from state.\n    BBUF.set(B2B_IV, 16); // Second half from IV.\n    let { h, l } = u64.fromBig(BigInt(this.length));\n    BBUF[24] = B2B_IV[8] ^ l; // Low word of the offset.\n    BBUF[25] = B2B_IV[9] ^ h; // High word.\n    // Invert all bits for last block\n    if (isLast) {\n      BBUF[28] = ~BBUF[28];\n      BBUF[29] = ~BBUF[29];\n    }\n    let j = 0;\n    const s = BSIGMA;\n    for (let i = 0; i < 12; i++) {\n      G1b(0, 4, 8, 12, msg, offset + 2 * s[j++]);\n      G2b(0, 4, 8, 12, msg, offset + 2 * s[j++]);\n      G1b(1, 5, 9, 13, msg, offset + 2 * s[j++]);\n      G2b(1, 5, 9, 13, msg, offset + 2 * s[j++]);\n      G1b(2, 6, 10, 14, msg, offset + 2 * s[j++]);\n      G2b(2, 6, 10, 14, msg, offset + 2 * s[j++]);\n      G1b(3, 7, 11, 15, msg, offset + 2 * s[j++]);\n      G2b(3, 7, 11, 15, msg, offset + 2 * s[j++]);\n\n      G1b(0, 5, 10, 15, msg, offset + 2 * s[j++]);\n      G2b(0, 5, 10, 15, msg, offset + 2 * s[j++]);\n      G1b(1, 6, 11, 12, msg, offset + 2 * s[j++]);\n      G2b(1, 6, 11, 12, msg, offset + 2 * s[j++]);\n      G1b(2, 7, 8, 13, msg, offset + 2 * s[j++]);\n      G2b(2, 7, 8, 13, msg, offset + 2 * s[j++]);\n      G1b(3, 4, 9, 14, msg, offset + 2 * s[j++]);\n      G2b(3, 4, 9, 14, msg, offset + 2 * s[j++]);\n    }\n    this.v0l ^= BBUF[0] ^ BBUF[16];\n    this.v0h ^= BBUF[1] ^ BBUF[17];\n    this.v1l ^= BBUF[2] ^ BBUF[18];\n    this.v1h ^= BBUF[3] ^ BBUF[19];\n    this.v2l ^= BBUF[4] ^ BBUF[20];\n    this.v2h ^= BBUF[5] ^ BBUF[21];\n    this.v3l ^= BBUF[6] ^ BBUF[22];\n    this.v3h ^= BBUF[7] ^ BBUF[23];\n    this.v4l ^= BBUF[8] ^ BBUF[24];\n    this.v4h ^= BBUF[9] ^ BBUF[25];\n    this.v5l ^= BBUF[10] ^ BBUF[26];\n    this.v5h ^= BBUF[11] ^ BBUF[27];\n    this.v6l ^= BBUF[12] ^ BBUF[28];\n    this.v6h ^= BBUF[13] ^ BBUF[29];\n    this.v7l ^= BBUF[14] ^ BBUF[30];\n    this.v7h ^= BBUF[15] ^ BBUF[31];\n    clean(BBUF);\n  }\n  destroy(): void {\n    this.destroyed = true;\n    clean(this.buffer32);\n    this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n  }\n}\n\n/**\n * Blake2b hash function. 64-bit. 1.5x slower than blake2s in JS.\n * @param msg - message that would be hashed\n * @param opts - dkLen output length, key for MAC mode, salt, personalization\n */\nexport const blake2b: CHashO = /* @__PURE__ */ createOptHasher<BLAKE2b, Blake2Opts>(\n  (opts) => new BLAKE2b(opts)\n);\n\n// =================\n// Blake2S\n// =================\n\n// prettier-ignore\nexport type Num16 = {\n  v0: number; v1: number; v2: number; v3: number;\n  v4: number; v5: number; v6: number; v7: number;\n  v8: number; v9: number; v10: number; v11: number;\n  v12: number; v13: number; v14: number; v15: number;\n};\n\n// prettier-ignore\nexport function compress(s: Uint8Array, offset: number, msg: Uint32Array, rounds: number,\n  v0: number, v1: number, v2: number, v3: number, v4: number, v5: number, v6: number, v7: number,\n  v8: number, v9: number, v10: number, v11: number, v12: number, v13: number, v14: number, v15: number,\n): Num16 {\n  let j = 0;\n  for (let i = 0; i < rounds; i++) {\n    ({ a: v0, b: v4, c: v8, d: v12 } = G1s(v0, v4, v8, v12, msg[offset + s[j++]]));\n    ({ a: v0, b: v4, c: v8, d: v12 } = G2s(v0, v4, v8, v12, msg[offset + s[j++]]));\n    ({ a: v1, b: v5, c: v9, d: v13 } = G1s(v1, v5, v9, v13, msg[offset + s[j++]]));\n    ({ a: v1, b: v5, c: v9, d: v13 } = G2s(v1, v5, v9, v13, msg[offset + s[j++]]));\n    ({ a: v2, b: v6, c: v10, d: v14 } = G1s(v2, v6, v10, v14, msg[offset + s[j++]]));\n    ({ a: v2, b: v6, c: v10, d: v14 } = G2s(v2, v6, v10, v14, msg[offset + s[j++]]));\n    ({ a: v3, b: v7, c: v11, d: v15 } = G1s(v3, v7, v11, v15, msg[offset + s[j++]]));\n    ({ a: v3, b: v7, c: v11, d: v15 } = G2s(v3, v7, v11, v15, msg[offset + s[j++]]));\n\n    ({ a: v0, b: v5, c: v10, d: v15 } = G1s(v0, v5, v10, v15, msg[offset + s[j++]]));\n    ({ a: v0, b: v5, c: v10, d: v15 } = G2s(v0, v5, v10, v15, msg[offset + s[j++]]));\n    ({ a: v1, b: v6, c: v11, d: v12 } = G1s(v1, v6, v11, v12, msg[offset + s[j++]]));\n    ({ a: v1, b: v6, c: v11, d: v12 } = G2s(v1, v6, v11, v12, msg[offset + s[j++]]));\n    ({ a: v2, b: v7, c: v8, d: v13 } = G1s(v2, v7, v8, v13, msg[offset + s[j++]]));\n    ({ a: v2, b: v7, c: v8, d: v13 } = G2s(v2, v7, v8, v13, msg[offset + s[j++]]));\n    ({ a: v3, b: v4, c: v9, d: v14 } = G1s(v3, v4, v9, v14, msg[offset + s[j++]]));\n    ({ a: v3, b: v4, c: v9, d: v14 } = G2s(v3, v4, v9, v14, msg[offset + s[j++]]));\n  }\n  return { v0, v1, v2, v3, v4, v5, v6, v7, v8, v9, v10, v11, v12, v13, v14, v15 };\n}\n\nconst B2S_IV = SHA256_IV;\nexport class BLAKE2s extends BLAKE2<BLAKE2s> {\n  // Internal state, same as SHA-256\n  private v0 = B2S_IV[0] | 0;\n  private v1 = B2S_IV[1] | 0;\n  private v2 = B2S_IV[2] | 0;\n  private v3 = B2S_IV[3] | 0;\n  private v4 = B2S_IV[4] | 0;\n  private v5 = B2S_IV[5] | 0;\n  private v6 = B2S_IV[6] | 0;\n  private v7 = B2S_IV[7] | 0;\n\n  constructor(opts: Blake2Opts = {}) {\n    const olen = opts.dkLen === undefined ? 32 : opts.dkLen;\n    super(64, olen);\n    checkBlake2Opts(olen, opts, 32, 8, 8);\n    let { key, personalization, salt } = opts;\n    let keyLength = 0;\n    if (key !== undefined) {\n      key = toBytes(key);\n      keyLength = key.length;\n    }\n    this.v0 ^= this.outputLen | (keyLength << 8) | (0x01 << 16) | (0x01 << 24);\n    if (salt !== undefined) {\n      salt = toBytes(salt);\n      const slt = u32(salt as Uint8Array);\n      this.v4 ^= swap8IfBE(slt[0]);\n      this.v5 ^= swap8IfBE(slt[1]);\n    }\n    if (personalization !== undefined) {\n      personalization = toBytes(personalization);\n      const pers = u32(personalization as Uint8Array);\n      this.v6 ^= swap8IfBE(pers[0]);\n      this.v7 ^= swap8IfBE(pers[1]);\n    }\n    if (key !== undefined) {\n      // Pad to blockLen and update\n      abytes(key);\n      const tmp = new Uint8Array(this.blockLen);\n      tmp.set(key);\n      this.update(tmp);\n    }\n  }\n  protected get(): [number, number, number, number, number, number, number, number] {\n    const { v0, v1, v2, v3, v4, v5, v6, v7 } = this;\n    return [v0, v1, v2, v3, v4, v5, v6, v7];\n  }\n  // prettier-ignore\n  protected set(\n    v0: number, v1: number, v2: number, v3: number, v4: number, v5: number, v6: number, v7: number\n  ): void {\n    this.v0 = v0 | 0;\n    this.v1 = v1 | 0;\n    this.v2 = v2 | 0;\n    this.v3 = v3 | 0;\n    this.v4 = v4 | 0;\n    this.v5 = v5 | 0;\n    this.v6 = v6 | 0;\n    this.v7 = v7 | 0;\n  }\n  protected compress(msg: Uint32Array, offset: number, isLast: boolean): void {\n    const { h, l } = u64.fromBig(BigInt(this.length));\n    // prettier-ignore\n    const { v0, v1, v2, v3, v4, v5, v6, v7, v8, v9, v10, v11, v12, v13, v14, v15 } =\n      compress(\n        BSIGMA, offset, msg, 10,\n        this.v0, this.v1, this.v2, this.v3, this.v4, this.v5, this.v6, this.v7,\n        B2S_IV[0], B2S_IV[1], B2S_IV[2], B2S_IV[3], l ^ B2S_IV[4], h ^ B2S_IV[5], isLast ? ~B2S_IV[6] : B2S_IV[6], B2S_IV[7]\n      );\n    this.v0 ^= v0 ^ v8;\n    this.v1 ^= v1 ^ v9;\n    this.v2 ^= v2 ^ v10;\n    this.v3 ^= v3 ^ v11;\n    this.v4 ^= v4 ^ v12;\n    this.v5 ^= v5 ^ v13;\n    this.v6 ^= v6 ^ v14;\n    this.v7 ^= v7 ^ v15;\n  }\n  destroy(): void {\n    this.destroyed = true;\n    clean(this.buffer32);\n    this.set(0, 0, 0, 0, 0, 0, 0, 0);\n  }\n}\n\n/**\n * Blake2s hash function. Focuses on 8-bit to 32-bit platforms. 1.5x faster than blake2b in JS.\n * @param msg - message that would be hashed\n * @param opts - dkLen output length, key for MAC mode, salt, personalization\n */\nexport const blake2s: CHashO = /* @__PURE__ */ createOptHasher<BLAKE2s, Blake2Opts>(\n  (opts) => new BLAKE2s(opts)\n);\n", "/**\n * Blake2b hash function. Focuses on 64-bit platforms, but in JS speed different from Blake2s is negligible.\n * @module\n * @deprecated\n */\nimport { BLAKE2b as B2B, blake2b as b2b } from './blake2.ts';\n/** @deprecated Use import from `noble/hashes/blake2` module */\nexport const BLAKE2b: typeof B2B = B2B;\n/** @deprecated Use import from `noble/hashes/blake2` module */\nexport const blake2b: typeof b2b = b2b;\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { splitGenericParameters } from '@mysten/bcs';\n\nimport { normalizeSuiAddress } from '../utils/sui-types.js';\nimport type { TypeTag } from './types.js';\n\nconst VECTOR_REGEX = /^vector<(.+)>$/;\nconst STRUCT_REGEX = /^([^:]+)::([^:]+)::([^<]+)(<(.+)>)?/;\n\nexport class TypeTagSerializer {\n\tstatic parseFromStr(str: string, normalizeAddress = false): TypeTag {\n\t\tif (str === 'address') {\n\t\t\treturn { address: null };\n\t\t} else if (str === 'bool') {\n\t\t\treturn { bool: null };\n\t\t} else if (str === 'u8') {\n\t\t\treturn { u8: null };\n\t\t} else if (str === 'u16') {\n\t\t\treturn { u16: null };\n\t\t} else if (str === 'u32') {\n\t\t\treturn { u32: null };\n\t\t} else if (str === 'u64') {\n\t\t\treturn { u64: null };\n\t\t} else if (str === 'u128') {\n\t\t\treturn { u128: null };\n\t\t} else if (str === 'u256') {\n\t\t\treturn { u256: null };\n\t\t} else if (str === 'signer') {\n\t\t\treturn { signer: null };\n\t\t}\n\n\t\tconst vectorMatch = str.match(VECTOR_REGEX);\n\t\tif (vectorMatch) {\n\t\t\treturn {\n\t\t\t\tvector: TypeTagSerializer.parseFromStr(vectorMatch[1], normalizeAddress),\n\t\t\t};\n\t\t}\n\n\t\tconst structMatch = str.match(STRUCT_REGEX);\n\t\tif (structMatch) {\n\t\t\tconst address = normalizeAddress ? normalizeSuiAddress(structMatch[1]) : structMatch[1];\n\t\t\treturn {\n\t\t\t\tstruct: {\n\t\t\t\t\taddress,\n\t\t\t\t\tmodule: structMatch[2],\n\t\t\t\t\tname: structMatch[3],\n\t\t\t\t\ttypeParams:\n\t\t\t\t\t\tstructMatch[5] === undefined\n\t\t\t\t\t\t\t? []\n\t\t\t\t\t\t\t: TypeTagSerializer.parseStructTypeArgs(structMatch[5], normalizeAddress),\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\tthrow new Error(`Encountered unexpected token when parsing type args for ${str}`);\n\t}\n\n\tstatic parseStructTypeArgs(str: string, normalizeAddress = false): TypeTag[] {\n\t\treturn splitGenericParameters(str).map((tok) =>\n\t\t\tTypeTagSerializer.parseFromStr(tok, normalizeAddress),\n\t\t);\n\t}\n\n\tstatic tagToString(tag: TypeTag): string {\n\t\tif ('bool' in tag) {\n\t\t\treturn 'bool';\n\t\t}\n\t\tif ('u8' in tag) {\n\t\t\treturn 'u8';\n\t\t}\n\t\tif ('u16' in tag) {\n\t\t\treturn 'u16';\n\t\t}\n\t\tif ('u32' in tag) {\n\t\t\treturn 'u32';\n\t\t}\n\t\tif ('u64' in tag) {\n\t\t\treturn 'u64';\n\t\t}\n\t\tif ('u128' in tag) {\n\t\t\treturn 'u128';\n\t\t}\n\t\tif ('u256' in tag) {\n\t\t\treturn 'u256';\n\t\t}\n\t\tif ('address' in tag) {\n\t\t\treturn 'address';\n\t\t}\n\t\tif ('signer' in tag) {\n\t\t\treturn 'signer';\n\t\t}\n\t\tif ('vector' in tag) {\n\t\t\treturn `vector<${TypeTagSerializer.tagToString(tag.vector)}>`;\n\t\t}\n\t\tif ('struct' in tag) {\n\t\t\tconst struct = tag.struct;\n\t\t\tconst typeParams = struct.typeParams.map(TypeTagSerializer.tagToString).join(', ');\n\t\t\treturn `${struct.address}::${struct.module}::${struct.name}${\n\t\t\t\ttypeParams ? `<${typeParams}>` : ''\n\t\t\t}`;\n\t\t}\n\t\tthrow new Error('Invalid TypeTag');\n\t}\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { BcsType, BcsTypeOptions } from '@mysten/bcs';\nimport { bcs, fromBase58, fromBase64, fromHex, toBase58, toBase64, toHex } from '@mysten/bcs';\n\nimport { isValidSuiAddress, normalizeSuiAddress, SUI_ADDRESS_LENGTH } from '../utils/sui-types.js';\nimport { TypeTagSerializer } from './type-tag-serializer.js';\nimport type { TypeTag as TypeTagType } from './types.js';\n\nfunction unsafe_u64(options?: BcsTypeOptions<number>) {\n\treturn bcs\n\t\t.u64({\n\t\t\tname: 'unsafe_u64',\n\t\t\t...(options as object),\n\t\t})\n\t\t.transform({\n\t\t\tinput: (val: number | string) => val,\n\t\t\toutput: (val) => Number(val),\n\t\t});\n}\n\nfunction optionEnum<T extends BcsType<any, any>>(type: T) {\n\treturn bcs.enum('Option', {\n\t\tNone: null,\n\t\tSome: type,\n\t});\n}\n\nexport const Address = bcs.bytes(SUI_ADDRESS_LENGTH).transform({\n\tvalidate: (val) => {\n\t\tconst address = typeof val === 'string' ? val : toHex(val);\n\t\tif (!address || !isValidSuiAddress(normalizeSuiAddress(address))) {\n\t\t\tthrow new Error(`Invalid Sui address ${address}`);\n\t\t}\n\t},\n\tinput: (val: string | Uint8Array) =>\n\t\ttypeof val === 'string' ? fromHex(normalizeSuiAddress(val)) : val,\n\toutput: (val) => normalizeSuiAddress(toHex(val)),\n});\n\nexport const ObjectDigest = bcs.vector(bcs.u8()).transform({\n\tname: 'ObjectDigest',\n\tinput: (value: string) => fromBase58(value),\n\toutput: (value) => toBase58(new Uint8Array(value)),\n\tvalidate: (value) => {\n\t\tif (fromBase58(value).length !== 32) {\n\t\t\tthrow new Error('ObjectDigest must be 32 bytes');\n\t\t}\n\t},\n});\n\nexport const SuiObjectRef = bcs.struct('SuiObjectRef', {\n\tobjectId: Address,\n\tversion: bcs.u64(),\n\tdigest: ObjectDigest,\n});\n\nexport const SharedObjectRef = bcs.struct('SharedObjectRef', {\n\tobjectId: Address,\n\tinitialSharedVersion: bcs.u64(),\n\tmutable: bcs.bool(),\n});\n\nexport const ObjectArg = bcs.enum('ObjectArg', {\n\tImmOrOwnedObject: SuiObjectRef,\n\tSharedObject: SharedObjectRef,\n\tReceiving: SuiObjectRef,\n});\n\nexport const Owner = bcs.enum('Owner', {\n\tAddressOwner: Address,\n\tObjectOwner: Address,\n\tShared: bcs.struct('Shared', {\n\t\tinitialSharedVersion: bcs.u64(),\n\t}),\n\tImmutable: null,\n\tConsensusAddressOwner: bcs.struct('ConsensusAddressOwner', {\n\t\towner: Address,\n\t\tstartVersion: bcs.u64(),\n\t}),\n});\n\nexport const CallArg = bcs.enum('CallArg', {\n\tPure: bcs.struct('Pure', {\n\t\tbytes: bcs.vector(bcs.u8()).transform({\n\t\t\tinput: (val: string | Uint8Array) => (typeof val === 'string' ? fromBase64(val) : val),\n\t\t\toutput: (val) => toBase64(new Uint8Array(val)),\n\t\t}),\n\t}),\n\tObject: ObjectArg,\n});\n\nconst InnerTypeTag: BcsType<TypeTagType, TypeTagType> = bcs.enum('TypeTag', {\n\tbool: null,\n\tu8: null,\n\tu64: null,\n\tu128: null,\n\taddress: null,\n\tsigner: null,\n\tvector: bcs.lazy(() => InnerTypeTag),\n\tstruct: bcs.lazy(() => StructTag),\n\tu16: null,\n\tu32: null,\n\tu256: null,\n}) as BcsType<TypeTagType>;\n\nexport const TypeTag = InnerTypeTag.transform({\n\tinput: (typeTag: string | TypeTagType) =>\n\t\ttypeof typeTag === 'string' ? TypeTagSerializer.parseFromStr(typeTag, true) : typeTag,\n\toutput: (typeTag: TypeTagType) => TypeTagSerializer.tagToString(typeTag),\n});\n\nexport const Argument = bcs.enum('Argument', {\n\tGasCoin: null,\n\tInput: bcs.u16(),\n\tResult: bcs.u16(),\n\tNestedResult: bcs.tuple([bcs.u16(), bcs.u16()]),\n});\n\nexport const ProgrammableMoveCall = bcs.struct('ProgrammableMoveCall', {\n\tpackage: Address,\n\tmodule: bcs.string(),\n\tfunction: bcs.string(),\n\ttypeArguments: bcs.vector(TypeTag),\n\targuments: bcs.vector(Argument),\n});\n\nexport const Command = bcs.enum('Command', {\n\t/**\n\t * A Move Call - any public Move function can be called via\n\t * this transaction. The results can be used that instant to pass\n\t * into the next transaction.\n\t */\n\tMoveCall: ProgrammableMoveCall,\n\t/**\n\t * Transfer vector of objects to a receiver.\n\t */\n\tTransferObjects: bcs.struct('TransferObjects', {\n\t\tobjects: bcs.vector(Argument),\n\t\taddress: Argument,\n\t}),\n\t// /**\n\t//  * Split `amount` from a `coin`.\n\t//  */\n\tSplitCoins: bcs.struct('SplitCoins', {\n\t\tcoin: Argument,\n\t\tamounts: bcs.vector(Argument),\n\t}),\n\t// /**\n\t//  * Merge Vector of Coins (`sources`) into a `destination`.\n\t//  */\n\tMergeCoins: bcs.struct('MergeCoins', {\n\t\tdestination: Argument,\n\t\tsources: bcs.vector(Argument),\n\t}),\n\t// /**\n\t//  * Publish a Move module.\n\t//  */\n\tPublish: bcs.struct('Publish', {\n\t\tmodules: bcs.vector(\n\t\t\tbcs.vector(bcs.u8()).transform({\n\t\t\t\tinput: (val: string | Uint8Array) => (typeof val === 'string' ? fromBase64(val) : val),\n\t\t\t\toutput: (val) => toBase64(new Uint8Array(val)),\n\t\t\t}),\n\t\t),\n\t\tdependencies: bcs.vector(Address),\n\t}),\n\t// /**\n\t//  * Build a vector of objects using the input arguments.\n\t//  * It is impossible to export construct a `vector<T: key>` otherwise,\n\t//  * so this call serves a utility function.\n\t//  */\n\tMakeMoveVec: bcs.struct('MakeMoveVec', {\n\t\ttype: optionEnum(TypeTag).transform({\n\t\t\tinput: (val: string | null) =>\n\t\t\t\tval === null\n\t\t\t\t\t? {\n\t\t\t\t\t\t\tNone: true,\n\t\t\t\t\t\t}\n\t\t\t\t\t: {\n\t\t\t\t\t\t\tSome: val,\n\t\t\t\t\t\t},\n\t\t\toutput: (val) => val.Some ?? null,\n\t\t}),\n\t\telements: bcs.vector(Argument),\n\t}),\n\tUpgrade: bcs.struct('Upgrade', {\n\t\tmodules: bcs.vector(\n\t\t\tbcs.vector(bcs.u8()).transform({\n\t\t\t\tinput: (val: string | Uint8Array) => (typeof val === 'string' ? fromBase64(val) : val),\n\t\t\t\toutput: (val) => toBase64(new Uint8Array(val)),\n\t\t\t}),\n\t\t),\n\t\tdependencies: bcs.vector(Address),\n\t\tpackage: Address,\n\t\tticket: Argument,\n\t}),\n});\n\nexport const ProgrammableTransaction = bcs.struct('ProgrammableTransaction', {\n\tinputs: bcs.vector(CallArg),\n\tcommands: bcs.vector(Command),\n});\n\nexport const TransactionKind = bcs.enum('TransactionKind', {\n\tProgrammableTransaction: ProgrammableTransaction,\n\tChangeEpoch: null,\n\tGenesis: null,\n\tConsensusCommitPrologue: null,\n});\n\nexport const TransactionExpiration = bcs.enum('TransactionExpiration', {\n\tNone: null,\n\tEpoch: unsafe_u64(),\n});\n\nexport const StructTag = bcs.struct('StructTag', {\n\taddress: Address,\n\tmodule: bcs.string(),\n\tname: bcs.string(),\n\ttypeParams: bcs.vector(InnerTypeTag),\n});\n\nexport const GasData = bcs.struct('GasData', {\n\tpayment: bcs.vector(SuiObjectRef),\n\towner: Address,\n\tprice: bcs.u64(),\n\tbudget: bcs.u64(),\n});\n\nexport const TransactionDataV1 = bcs.struct('TransactionDataV1', {\n\tkind: TransactionKind,\n\tsender: Address,\n\tgasData: GasData,\n\texpiration: TransactionExpiration,\n});\n\nexport const TransactionData = bcs.enum('TransactionData', {\n\tV1: TransactionDataV1,\n});\n\nexport const IntentScope = bcs.enum('IntentScope', {\n\tTransactionData: null,\n\tTransactionEffects: null,\n\tCheckpointSummary: null,\n\tPersonalMessage: null,\n});\n\nexport const IntentVersion = bcs.enum('IntentVersion', {\n\tV0: null,\n});\n\nexport const AppId = bcs.enum('AppId', {\n\tSui: null,\n});\n\nexport const Intent = bcs.struct('Intent', {\n\tscope: IntentScope,\n\tversion: IntentVersion,\n\tappId: AppId,\n});\n\nexport function IntentMessage<T extends BcsType<any>>(T: T) {\n\treturn bcs.struct(`IntentMessage<${T.name}>`, {\n\t\tintent: Intent,\n\t\tvalue: T,\n\t});\n}\n\nexport const CompressedSignature = bcs.enum('CompressedSignature', {\n\tED25519: bcs.fixedArray(64, bcs.u8()),\n\tSecp256k1: bcs.fixedArray(64, bcs.u8()),\n\tSecp256r1: bcs.fixedArray(64, bcs.u8()),\n\tZkLogin: bcs.vector(bcs.u8()),\n\tPasskey: bcs.vector(bcs.u8()),\n});\n\nexport const PublicKey = bcs.enum('PublicKey', {\n\tED25519: bcs.fixedArray(32, bcs.u8()),\n\tSecp256k1: bcs.fixedArray(33, bcs.u8()),\n\tSecp256r1: bcs.fixedArray(33, bcs.u8()),\n\tZkLogin: bcs.vector(bcs.u8()),\n\tPasskey: bcs.fixedArray(33, bcs.u8()),\n});\n\nexport const MultiSigPkMap = bcs.struct('MultiSigPkMap', {\n\tpubKey: PublicKey,\n\tweight: bcs.u8(),\n});\n\nexport const MultiSigPublicKey = bcs.struct('MultiSigPublicKey', {\n\tpk_map: bcs.vector(MultiSigPkMap),\n\tthreshold: bcs.u16(),\n});\n\nexport const MultiSig = bcs.struct('MultiSig', {\n\tsigs: bcs.vector(CompressedSignature),\n\tbitmap: bcs.u16(),\n\tmultisig_pk: MultiSigPublicKey,\n});\n\nexport const base64String = bcs.vector(bcs.u8()).transform({\n\tinput: (val: string | Uint8Array) => (typeof val === 'string' ? fromBase64(val) : val),\n\toutput: (val) => toBase64(new Uint8Array(val)),\n});\n\nexport const SenderSignedTransaction = bcs.struct('SenderSignedTransaction', {\n\tintentMessage: IntentMessage(TransactionData),\n\ttxSignatures: bcs.vector(base64String),\n});\n\nexport const SenderSignedData = bcs.vector(SenderSignedTransaction, {\n\tname: 'SenderSignedData',\n});\n\nexport const PasskeyAuthenticator = bcs.struct('PasskeyAuthenticator', {\n\tauthenticatorData: bcs.vector(bcs.u8()),\n\tclientDataJson: bcs.string(),\n\tuserSignature: bcs.vector(bcs.u8()),\n});\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { bcs } from '@mysten/bcs';\n\nimport { Address, ObjectDigest, Owner, SuiObjectRef } from './bcs.js';\n\nconst PackageUpgradeError = bcs.enum('PackageUpgradeError', {\n\tUnableToFetchPackage: bcs.struct('UnableToFetchPackage', { packageId: Address }),\n\tNotAPackage: bcs.struct('NotAPackage', { objectId: Address }),\n\tIncompatibleUpgrade: null,\n\tDigestDoesNotMatch: bcs.struct('DigestDoesNotMatch', { digest: bcs.vector(bcs.u8()) }),\n\tUnknownUpgradePolicy: bcs.struct('UnknownUpgradePolicy', { policy: bcs.u8() }),\n\tPackageIDDoesNotMatch: bcs.struct('PackageIDDoesNotMatch', {\n\t\tpackageId: Address,\n\t\tticketId: Address,\n\t}),\n});\n\nconst ModuleId = bcs.struct('ModuleId', {\n\taddress: Address,\n\tname: bcs.string(),\n});\nconst MoveLocation = bcs.struct('MoveLocation', {\n\tmodule: ModuleId,\n\tfunction: bcs.u16(),\n\tinstruction: bcs.u16(),\n\tfunctionName: bcs.option(bcs.string()),\n});\n\nconst CommandArgumentError = bcs.enum('CommandArgumentError', {\n\tTypeMismatch: null,\n\tInvalidBCSBytes: null,\n\tInvalidUsageOfPureArg: null,\n\tInvalidArgumentToPrivateEntryFunction: null,\n\tIndexOutOfBounds: bcs.struct('IndexOutOfBounds', { idx: bcs.u16() }),\n\tSecondaryIndexOutOfBounds: bcs.struct('SecondaryIndexOutOfBounds', {\n\t\tresultIdx: bcs.u16(),\n\t\tsecondaryIdx: bcs.u16(),\n\t}),\n\tInvalidResultArity: bcs.struct('InvalidResultArity', { resultIdx: bcs.u16() }),\n\tInvalidGasCoinUsage: null,\n\tInvalidValueUsage: null,\n\tInvalidObjectByValue: null,\n\tInvalidObjectByMutRef: null,\n\tSharedObjectOperationNotAllowed: null,\n});\n\nconst TypeArgumentError = bcs.enum('TypeArgumentError', {\n\tTypeNotFound: null,\n\tConstraintNotSatisfied: null,\n});\n\nconst ExecutionFailureStatus = bcs.enum('ExecutionFailureStatus', {\n\tInsufficientGas: null,\n\tInvalidGasObject: null,\n\tInvariantViolation: null,\n\tFeatureNotYetSupported: null,\n\tMoveObjectTooBig: bcs.struct('MoveObjectTooBig', {\n\t\tobjectSize: bcs.u64(),\n\t\tmaxObjectSize: bcs.u64(),\n\t}),\n\tMovePackageTooBig: bcs.struct('MovePackageTooBig', {\n\t\tobjectSize: bcs.u64(),\n\t\tmaxObjectSize: bcs.u64(),\n\t}),\n\tCircularObjectOwnership: bcs.struct('CircularObjectOwnership', { object: Address }),\n\tInsufficientCoinBalance: null,\n\tCoinBalanceOverflow: null,\n\tPublishErrorNonZeroAddress: null,\n\tSuiMoveVerificationError: null,\n\tMovePrimitiveRuntimeError: bcs.option(MoveLocation),\n\tMoveAbort: bcs.tuple([MoveLocation, bcs.u64()]),\n\tVMVerificationOrDeserializationError: null,\n\tVMInvariantViolation: null,\n\tFunctionNotFound: null,\n\tArityMismatch: null,\n\tTypeArityMismatch: null,\n\tNonEntryFunctionInvoked: null,\n\tCommandArgumentError: bcs.struct('CommandArgumentError', {\n\t\targIdx: bcs.u16(),\n\t\tkind: CommandArgumentError,\n\t}),\n\tTypeArgumentError: bcs.struct('TypeArgumentError', {\n\t\targumentIdx: bcs.u16(),\n\t\tkind: TypeArgumentError,\n\t}),\n\tUnusedValueWithoutDrop: bcs.struct('UnusedValueWithoutDrop', {\n\t\tresultIdx: bcs.u16(),\n\t\tsecondaryIdx: bcs.u16(),\n\t}),\n\tInvalidPublicFunctionReturnType: bcs.struct('InvalidPublicFunctionReturnType', {\n\t\tidx: bcs.u16(),\n\t}),\n\tInvalidTransferObject: null,\n\tEffectsTooLarge: bcs.struct('EffectsTooLarge', { currentSize: bcs.u64(), maxSize: bcs.u64() }),\n\tPublishUpgradeMissingDependency: null,\n\tPublishUpgradeDependencyDowngrade: null,\n\tPackageUpgradeError: bcs.struct('PackageUpgradeError', { upgradeError: PackageUpgradeError }),\n\tWrittenObjectsTooLarge: bcs.struct('WrittenObjectsTooLarge', {\n\t\tcurrentSize: bcs.u64(),\n\t\tmaxSize: bcs.u64(),\n\t}),\n\tCertificateDenied: null,\n\tSuiMoveVerificationTimedout: null,\n\tSharedObjectOperationNotAllowed: null,\n\tInputObjectDeleted: null,\n\tExecutionCancelledDueToSharedObjectCongestion: bcs.struct(\n\t\t'ExecutionCancelledDueToSharedObjectCongestion',\n\t\t{\n\t\t\tcongestedObjects: bcs.vector(Address),\n\t\t},\n\t),\n\tAddressDeniedForCoin: bcs.struct('AddressDeniedForCoin', {\n\t\taddress: Address,\n\t\tcoinType: bcs.string(),\n\t}),\n\tCoinTypeGlobalPause: bcs.struct('CoinTypeGlobalPause', { coinType: bcs.string() }),\n\tExecutionCancelledDueToRandomnessUnavailable: null,\n});\n\nconst ExecutionStatus = bcs.enum('ExecutionStatus', {\n\tSuccess: null,\n\tFailed: bcs.struct('ExecutionFailed', {\n\t\terror: ExecutionFailureStatus,\n\t\tcommand: bcs.option(bcs.u64()),\n\t}),\n});\n\nconst GasCostSummary = bcs.struct('GasCostSummary', {\n\tcomputationCost: bcs.u64(),\n\tstorageCost: bcs.u64(),\n\tstorageRebate: bcs.u64(),\n\tnonRefundableStorageFee: bcs.u64(),\n});\n\nconst TransactionEffectsV1 = bcs.struct('TransactionEffectsV1', {\n\tstatus: ExecutionStatus,\n\texecutedEpoch: bcs.u64(),\n\tgasUsed: GasCostSummary,\n\tmodifiedAtVersions: bcs.vector(bcs.tuple([Address, bcs.u64()])),\n\tsharedObjects: bcs.vector(SuiObjectRef),\n\ttransactionDigest: ObjectDigest,\n\tcreated: bcs.vector(bcs.tuple([SuiObjectRef, Owner])),\n\tmutated: bcs.vector(bcs.tuple([SuiObjectRef, Owner])),\n\tunwrapped: bcs.vector(bcs.tuple([SuiObjectRef, Owner])),\n\tdeleted: bcs.vector(SuiObjectRef),\n\tunwrappedThenDeleted: bcs.vector(SuiObjectRef),\n\twrapped: bcs.vector(SuiObjectRef),\n\tgasObject: bcs.tuple([SuiObjectRef, Owner]),\n\teventsDigest: bcs.option(ObjectDigest),\n\tdependencies: bcs.vector(ObjectDigest),\n});\n\nconst VersionDigest = bcs.tuple([bcs.u64(), ObjectDigest]);\n\nconst ObjectIn = bcs.enum('ObjectIn', {\n\tNotExist: null,\n\tExist: bcs.tuple([VersionDigest, Owner]),\n});\n\nconst ObjectOut = bcs.enum('ObjectOut', {\n\tNotExist: null,\n\tObjectWrite: bcs.tuple([ObjectDigest, Owner]),\n\tPackageWrite: VersionDigest,\n});\n\nconst IDOperation = bcs.enum('IDOperation', {\n\tNone: null,\n\tCreated: null,\n\tDeleted: null,\n});\n\nconst EffectsObjectChange = bcs.struct('EffectsObjectChange', {\n\tinputState: ObjectIn,\n\toutputState: ObjectOut,\n\tidOperation: IDOperation,\n});\n\nconst UnchangedSharedKind = bcs.enum('UnchangedSharedKind', {\n\tReadOnlyRoot: VersionDigest,\n\tMutateDeleted: bcs.u64(),\n\tReadDeleted: bcs.u64(),\n\tCancelled: bcs.u64(),\n\tPerEpochConfig: null,\n});\n\nconst TransactionEffectsV2 = bcs.struct('TransactionEffectsV2', {\n\tstatus: ExecutionStatus,\n\texecutedEpoch: bcs.u64(),\n\tgasUsed: GasCostSummary,\n\ttransactionDigest: ObjectDigest,\n\tgasObjectIndex: bcs.option(bcs.u32()),\n\teventsDigest: bcs.option(ObjectDigest),\n\tdependencies: bcs.vector(ObjectDigest),\n\tlamportVersion: bcs.u64(),\n\tchangedObjects: bcs.vector(bcs.tuple([Address, EffectsObjectChange])),\n\tunchangedSharedObjects: bcs.vector(bcs.tuple([Address, UnchangedSharedKind])),\n\tauxDataDigest: bcs.option(ObjectDigest),\n});\n\nexport const TransactionEffects = bcs.enum('TransactionEffects', {\n\tV1: TransactionEffectsV1,\n\tV2: TransactionEffectsV2,\n});\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { bcs } from '@mysten/bcs';\n\nimport {\n\tAddress,\n\tAppId,\n\tArgument,\n\tCallArg,\n\tCommand,\n\tCompressedSignature,\n\tGasData,\n\tIntent,\n\tIntentMessage,\n\tIntentScope,\n\tIntentVersion,\n\tMultiSig,\n\tMultiSigPkMap,\n\tMultiSigPublicKey,\n\tObjectArg,\n\tObjectDigest,\n\tOwner,\n\tPasskeyAuthenticator,\n\tProgrammableMoveCall,\n\tProgrammableTransaction,\n\tPublicKey,\n\tSenderSignedData,\n\tSenderSignedTransaction,\n\tSharedObjectRef,\n\tStructTag,\n\tSuiObjectRef,\n\tTransactionData,\n\tTransactionDataV1,\n\tTransactionExpiration,\n\tTransactionKind,\n\tTypeTag,\n} from './bcs.js';\nimport { TransactionEffects } from './effects.js';\n\nexport type { TypeTag } from './types.js';\n\nexport { TypeTagSerializer } from './type-tag-serializer.js';\nexport { BcsType, type BcsTypeOptions } from '@mysten/bcs';\n\nconst suiBcs = {\n\t...bcs,\n\tU8: bcs.u8(),\n\tU16: bcs.u16(),\n\tU32: bcs.u32(),\n\tU64: bcs.u64(),\n\tU128: bcs.u128(),\n\tU256: bcs.u256(),\n\tULEB128: bcs.uleb128(),\n\tBool: bcs.bool(),\n\tString: bcs.string(),\n\tAddress,\n\tAppId,\n\tArgument,\n\tCallArg,\n\tCommand,\n\tCompressedSignature,\n\tGasData,\n\tIntent,\n\tIntentMessage,\n\tIntentScope,\n\tIntentVersion,\n\tMultiSig,\n\tMultiSigPkMap,\n\tMultiSigPublicKey,\n\tObjectArg,\n\tObjectDigest,\n\tOwner,\n\tPasskeyAuthenticator,\n\tProgrammableMoveCall,\n\tProgrammableTransaction,\n\tPublicKey,\n\tSenderSignedData,\n\tSenderSignedTransaction,\n\tSharedObjectRef,\n\tStructTag,\n\tSuiObjectRef,\n\tTransactionData,\n\tTransactionDataV1,\n\tTransactionEffects,\n\tTransactionExpiration,\n\tTransactionKind,\n\tTypeTag,\n};\nexport {\n\tpureBcsSchemaFromTypeName,\n\ttype ShapeFromPureTypeName,\n\ttype PureTypeName,\n} from './pure.js';\n\nexport { suiBcs as bcs };\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { toHex } from '@mysten/bcs';\nimport { blake2b } from '@noble/hashes/blake2b';\n\nimport type { TypeTag } from '../bcs/bcs.js';\nimport { bcs } from '../bcs/index.js';\n\nexport function deriveDynamicFieldID(\n\tparentId: string,\n\ttypeTag: typeof TypeTag.$inferInput,\n\tkey: Uint8Array,\n) {\n\tconst address = bcs.Address.serialize(parentId).toBytes();\n\tconst tag = bcs.TypeTag.serialize(typeTag).toBytes();\n\tconst keyLength = bcs.u64().serialize(key.length).toBytes();\n\n\tconst hash = blake2b.create({\n\t\tdkLen: 32,\n\t});\n\n\thash.update(new Uint8Array([0xf0]));\n\thash.update(address);\n\thash.update(keyLength);\n\thash.update(key);\n\thash.update(tag);\n\n\treturn `0x${toHex(hash.digest().slice(0, 32))}`;\n}\n"], "mappings": ";AAGA,IAAM,WAAW;AAEV,SAAS,cAAc,SAAiB;AAC9C,MAAI,QAAQ,UAAU,GAAG;AACxB,WAAO;EACR;AAEA,QAAM,SAAS,QAAQ,WAAW,IAAI,IAAI,IAAI;AAE9C,SAAO,KAAK,QAAQ,MAAM,QAAQ,SAAS,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,MAAM,EAAE,CAAC;AAC7E;AAEO,SAAS,aAAa,QAAgB;AAE5C,SAAO,GAAG,OAAO,MAAM,GAAG,EAAE,CAAC,GAAG,QAAQ;AACzC;;;ACNA,SAAS,QAAQ,GAAU;AACzB,SAAO,aAAa,cAAe,YAAY,OAAO,CAAC,KAAK,EAAE,YAAY,SAAS;AACrF;AAEA,SAAS,OAAO,MAA8B,SAAiB;AAC7D,MAAI,CAAC,QAAQ,CAAC;AAAG,UAAM,IAAI,MAAM,qBAAqB;AACtD,MAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,MAAM;AAClD,UAAM,IAAI,MAAM,mCAAmC,UAAU,kBAAkB,EAAE,MAAM;AAC3F;AAEA,SAAS,UAAU,UAAmB,KAAU;AAC9C,MAAI,CAAC,MAAM,QAAQ,GAAG;AAAG,WAAO;AAChC,MAAI,IAAI,WAAW;AAAG,WAAO;AAC7B,MAAI,UAAU;AACZ,WAAO,IAAI,MAAM,CAAC,SAAS,OAAO,SAAS,QAAQ;EACrD,OAAO;AACL,WAAO,IAAI,MAAM,CAAC,SAAS,OAAO,cAAc,IAAI,CAAC;EACvD;AACF;AAIA,SAAS,IAAI,OAAe;AAC1B,MAAI,OAAO,UAAU;AAAY,UAAM,IAAI,MAAM,mBAAmB;AACpE,SAAO;AACT;AAEA,SAAS,KAAK,OAAe,OAAc;AACzC,MAAI,OAAO,UAAU;AAAU,UAAM,IAAI,MAAM,GAAG,KAAK,mBAAmB;AAC1E,SAAO;AACT;AAEA,SAAS,QAAQ,GAAS;AACxB,MAAI,CAAC,OAAO,cAAc,CAAC;AAAG,UAAM,IAAI,MAAM,oBAAoB,CAAC,EAAE;AACvE;AAEA,SAAS,KAAK,OAAY;AACxB,MAAI,CAAC,MAAM,QAAQ,KAAK;AAAG,UAAM,IAAI,MAAM,gBAAgB;AAC7D;AACA,SAAS,QAAQ,OAAe,OAAe;AAC7C,MAAI,CAAC,UAAU,MAAM,KAAK;AAAG,UAAM,IAAI,MAAM,GAAG,KAAK,6BAA6B;AACpF;AACA,SAAS,QAAQ,OAAe,OAAe;AAC7C,MAAI,CAAC,UAAU,OAAO,KAAK;AAAG,UAAM,IAAI,MAAM,GAAG,KAAK,6BAA6B;AACrF;AAqBA,SAAS,SAAuC,MAAO;AACrD,QAAM,KAAK,CAAC,MAAW;AAEvB,QAAM,OAAO,CAAC,GAAQ,MAAW,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC;AAEnD,QAAM,SAAS,KAAK,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,MAAM,EAAE;AAE7D,QAAM,SAAS,KAAK,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,MAAM,EAAE;AACxD,SAAO,EAAE,QAAQ,OAAM;AACzB;AAOA,SAAS,SAAS,SAA0B;AAE1C,QAAM,WAAW,OAAO,YAAY,WAAW,QAAQ,MAAM,EAAE,IAAI;AACnE,QAAM,MAAM,SAAS;AACrB,UAAQ,YAAY,QAAQ;AAG5B,QAAM,UAAU,IAAI,IAAI,SAAS,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACtD,SAAO;IACL,QAAQ,CAAC,WAAoB;AAC3B,WAAK,MAAM;AACX,aAAO,OAAO,IAAI,CAAC,MAAK;AACtB,YAAI,CAAC,OAAO,cAAc,CAAC,KAAK,IAAI,KAAK,KAAK;AAC5C,gBAAM,IAAI,MACR,kDAAkD,CAAC,eAAe,OAAO,EAAE;AAE/E,eAAO,SAAS,CAAC;MACnB,CAAC;IACH;IACA,QAAQ,CAAC,UAA6B;AACpC,WAAK,KAAK;AACV,aAAO,MAAM,IAAI,CAAC,WAAU;AAC1B,aAAK,mBAAmB,MAAM;AAC9B,cAAM,IAAI,QAAQ,IAAI,MAAM;AAC5B,YAAI,MAAM;AAAW,gBAAM,IAAI,MAAM,oBAAoB,MAAM,eAAe,OAAO,EAAE;AACvF,eAAO;MACT,CAAC;IACH;;AAEJ;AAKA,SAAS,KAAK,YAAY,IAAE;AAC1B,OAAK,QAAQ,SAAS;AACtB,SAAO;IACL,QAAQ,CAAC,SAAQ;AACf,cAAQ,eAAe,IAAI;AAC3B,aAAO,KAAK,KAAK,SAAS;IAC5B;IACA,QAAQ,CAAC,OAAM;AACb,WAAK,eAAe,EAAE;AACtB,aAAO,GAAG,MAAM,SAAS;IAC3B;;AAEJ;AAMA,SAAS,QAAQ,MAAc,MAAM,KAAG;AACtC,UAAQ,IAAI;AACZ,OAAK,WAAW,GAAG;AACnB,SAAO;IACL,OAAO,MAAc;AACnB,cAAQ,kBAAkB,IAAI;AAC9B,aAAQ,KAAK,SAAS,OAAQ;AAAG,aAAK,KAAK,GAAG;AAC9C,aAAO;IACT;IACA,OAAO,OAAe;AACpB,cAAQ,kBAAkB,KAAK;AAC/B,UAAI,MAAM,MAAM;AAChB,UAAK,MAAM,OAAQ;AACjB,cAAM,IAAI,MAAM,4DAA4D;AAC9E,aAAO,MAAM,KAAK,MAAM,MAAM,CAAC,MAAM,KAAK,OAAO;AAC/C,cAAM,OAAO,MAAM;AACnB,cAAM,OAAO,OAAO;AACpB,YAAI,OAAO,MAAM;AAAG,gBAAM,IAAI,MAAM,+CAA+C;MACrF;AACA,aAAO,MAAM,MAAM,GAAG,GAAG;IAC3B;;AAEJ;AAKA,SAAS,UAAa,IAAiB;AACrC,MAAI,EAAE;AACN,SAAO,EAAE,QAAQ,CAAC,SAAY,MAAM,QAAQ,CAAC,OAAU,GAAG,EAAE,EAAC;AAC/D;AAKA,SAAS,aAAa,MAAgB,MAAc,IAAU;AAE5D,MAAI,OAAO;AAAG,UAAM,IAAI,MAAM,8BAA8B,IAAI,8BAA8B;AAC9F,MAAI,KAAK;AAAG,UAAM,IAAI,MAAM,4BAA4B,EAAE,8BAA8B;AACxF,OAAK,IAAI;AACT,MAAI,CAAC,KAAK;AAAQ,WAAO,CAAA;AACzB,MAAI,MAAM;AACV,QAAM,MAAM,CAAA;AACZ,QAAM,SAAS,MAAM,KAAK,MAAM,CAAC,MAAK;AACpC,YAAQ,CAAC;AACT,QAAI,IAAI,KAAK,KAAK;AAAM,YAAM,IAAI,MAAM,oBAAoB,CAAC,EAAE;AAC/D,WAAO;EACT,CAAC;AACD,QAAM,OAAO,OAAO;AACpB,SAAO,MAAM;AACX,QAAI,QAAQ;AACZ,QAAI,OAAO;AACX,aAAS,IAAI,KAAK,IAAI,MAAM,KAAK;AAC/B,YAAM,QAAQ,OAAO,CAAC;AACtB,YAAM,YAAY,OAAO;AACzB,YAAM,YAAY,YAAY;AAC9B,UACE,CAAC,OAAO,cAAc,SAAS,KAC/B,YAAY,SAAS,SACrB,YAAY,UAAU,WACtB;AACA,cAAM,IAAI,MAAM,8BAA8B;MAChD;AACA,YAAM,MAAM,YAAY;AACxB,cAAQ,YAAY;AACpB,YAAM,UAAU,KAAK,MAAM,GAAG;AAC9B,aAAO,CAAC,IAAI;AACZ,UAAI,CAAC,OAAO,cAAc,OAAO,KAAK,UAAU,KAAK,UAAU;AAC7D,cAAM,IAAI,MAAM,8BAA8B;AAChD,UAAI,CAAC;AAAM;eACF,CAAC;AAAS,cAAM;;AACpB,eAAO;IACd;AACA,QAAI,KAAK,KAAK;AACd,QAAI;AAAM;EACZ;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,KAAK,KAAK,CAAC,MAAM,GAAG;AAAK,QAAI,KAAK,CAAC;AACrE,SAAO,IAAI,QAAO;AACpB;AAEA,IAAM,MAAM,CAAC,GAAW,MAAuB,MAAM,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC;AACzE,IAAM,cAAyC,CAAC,MAAc,OAC5D,QAAQ,KAAK,IAAI,MAAM,EAAE;AAC3B,IAAM,UAAoC,MAAK;AAC7C,MAAI,MAAM,CAAA;AACV,WAAS,IAAI,GAAG,IAAI,IAAI;AAAK,QAAI,KAAK,KAAK,CAAC;AAC5C,SAAO;AACT,GAAE;AAIF,SAAS,cAAc,MAAgB,MAAc,IAAYA,UAAgB;AAC/E,OAAK,IAAI;AACT,MAAI,QAAQ,KAAK,OAAO;AAAI,UAAM,IAAI,MAAM,6BAA6B,IAAI,EAAE;AAC/E,MAAI,MAAM,KAAK,KAAK;AAAI,UAAM,IAAI,MAAM,2BAA2B,EAAE,EAAE;AACvE,MAAI,YAAY,MAAM,EAAE,IAAI,IAAI;AAC9B,UAAM,IAAI,MACR,sCAAsC,IAAI,OAAO,EAAE,cAAc,YAAY,MAAM,EAAE,CAAC,EAAE;EAE5F;AACA,MAAI,QAAQ;AACZ,MAAI,MAAM;AACV,QAAM,MAAM,OAAO,IAAI;AACvB,QAAM,OAAO,OAAO,EAAE,IAAK;AAC3B,QAAM,MAAgB,CAAA;AACtB,aAAW,KAAK,MAAM;AACpB,YAAQ,CAAC;AACT,QAAI,KAAK;AAAK,YAAM,IAAI,MAAM,oCAAoC,CAAC,SAAS,IAAI,EAAE;AAClF,YAAS,SAAS,OAAQ;AAC1B,QAAI,MAAM,OAAO;AAAI,YAAM,IAAI,MAAM,qCAAqC,GAAG,SAAS,IAAI,EAAE;AAC5F,WAAO;AACP,WAAO,OAAO,IAAI,OAAO;AAAI,UAAI,MAAO,SAAU,MAAM,KAAO,UAAU,CAAC;AAC1E,UAAM,MAAM,OAAO,GAAG;AACtB,QAAI,QAAQ;AAAW,YAAM,IAAI,MAAM,eAAe;AACtD,aAAS,MAAM;EACjB;AACA,UAAS,SAAU,KAAK,MAAQ;AAChC,MAAI,CAACA,YAAW,OAAO;AAAM,UAAM,IAAI,MAAM,gBAAgB;AAC7D,MAAI,CAACA,YAAW,QAAQ;AAAG,UAAM,IAAI,MAAM,qBAAqB,KAAK,EAAE;AACvE,MAAIA,YAAW,MAAM;AAAG,QAAI,KAAK,UAAU,CAAC;AAC5C,SAAO;AACT;AAKA,SAAS,MAAM,KAAW;AACxB,UAAQ,GAAG;AACX,QAAM,OAAO,KAAK;AAClB,SAAO;IACL,QAAQ,CAAC,UAAqB;AAC5B,UAAI,CAAC,QAAQ,KAAK;AAAG,cAAM,IAAI,MAAM,yCAAyC;AAC9E,aAAO,aAAa,MAAM,KAAK,KAAK,GAAG,MAAM,GAAG;IAClD;IACA,QAAQ,CAAC,WAAoB;AAC3B,cAAQ,gBAAgB,MAAM;AAC9B,aAAO,WAAW,KAAK,aAAa,QAAQ,KAAK,IAAI,CAAC;IACxD;;AAEJ;AAOA,SAAS,OAAO,MAAc,aAAa,OAAK;AAC9C,UAAQ,IAAI;AACZ,MAAI,QAAQ,KAAK,OAAO;AAAI,UAAM,IAAI,MAAM,mCAAmC;AAC/E,MAAI,YAAY,GAAG,IAAI,IAAI,MAAM,YAAY,MAAM,CAAC,IAAI;AACtD,UAAM,IAAI,MAAM,wBAAwB;AAC1C,SAAO;IACL,QAAQ,CAAC,UAAqB;AAC5B,UAAI,CAAC,QAAQ,KAAK;AAAG,cAAM,IAAI,MAAM,0CAA0C;AAC/E,aAAO,cAAc,MAAM,KAAK,KAAK,GAAG,GAAG,MAAM,CAAC,UAAU;IAC9D;IACA,QAAQ,CAAC,WAAoB;AAC3B,cAAQ,iBAAiB,MAAM;AAC/B,aAAO,WAAW,KAAK,cAAc,QAAQ,MAAM,GAAG,UAAU,CAAC;IACnE;;AAEJ;AAGA,SAAS,cAA+C,IAAK;AAC3D,MAAI,EAAE;AACN,SAAO,YAAa,MAAsB;AACxC,QAAI;AACF,aAAO,GAAG,MAAM,MAAM,IAAI;IAC5B,SAAS,GAAG;IAAC;EACf;AACF;AA6CO,IAAM,SAAqB,MAAM,OAAO,CAAC,GAAG,SAAS,kBAAkB,GAAG,KAAK,EAAE,CAAC;AAclF,IAAM,SAAqB,MAChC,OAAO,CAAC,GACR,SAAS,kCAAkC,GAC3C,QAAQ,CAAC,GACT,KAAK,EAAE,CAAC;AAeH,IAAM,cAA0B,MACrC,OAAO,CAAC,GACR,SAAS,kCAAkC,GAC3C,KAAK,EAAE,CAAC;AAaH,IAAM,YAAwB,MACnC,OAAO,CAAC,GACR,SAAS,kCAAkC,GAC3C,QAAQ,CAAC,GACT,KAAK,EAAE,CAAC;AAcH,IAAM,iBAA6B,MACxC,OAAO,CAAC,GACR,SAAS,kCAAkC,GAC3C,KAAK,EAAE,CAAC;AAaH,IAAM,kBAA8B,MACzC,OAAO,CAAC,GACR,SAAS,kCAAkC,GAC3C,KAAK,EAAE,GACP,UAAU,CAAC,MAAc,EAAE,YAAW,EAAG,QAAQ,MAAM,GAAG,EAAE,QAAQ,SAAS,GAAG,CAAC,CAAC;AAKpF,IAAM,oBAA6C,MACjD,OAAQ,WAAmB,KAAK,CAAA,CAAE,EAAE,aAAa,cACjD,OAAQ,WAAmB,eAAe,YAAW;AAEvD,IAAM,sBAAsB,CAAC,GAAW,UAAkB;AACxD,OAAK,UAAU,CAAC;AAChB,QAAM,KAAK,QAAQ,sBAAsB;AACzC,QAAMC,YAAW,QAAQ,cAAc;AACvC,MAAI,EAAE,SAAS,KAAK,CAAC,GAAG,KAAK,CAAC;AAAG,UAAM,IAAI,MAAM,gBAAgB;AACjE,SAAQ,WAAmB,WAAW,GAAG,EAAE,UAAAA,WAAU,mBAAmB,SAAQ,CAAE;AACpF;AAgBO,IAAM,SAAqB,mBAAmB;EACnD,OAAO,GAAC;AAAI,WAAO,CAAC;AAAG,WAAQ,EAAU,SAAQ;EAAI;EACrD,OAAO,GAAC;AAAI,WAAO,oBAAoB,GAAG,KAAK;EAAG;IAChD,MACF,OAAO,CAAC,GACR,SAAS,kEAAkE,GAC3E,QAAQ,CAAC,GACT,KAAK,EAAE,CAAC;AAaH,IAAM,cAA0B,MACrC,OAAO,CAAC,GACR,SAAS,kEAAkE,GAC3E,KAAK,EAAE,CAAC;AAgBH,IAAM,YAAwB,mBAAmB;EACtD,OAAO,GAAC;AAAI,WAAO,CAAC;AAAG,WAAQ,EAAU,SAAS,EAAE,UAAU,YAAW,CAAE;EAAG;EAC9E,OAAO,GAAC;AAAI,WAAO,oBAAoB,GAAG,IAAI;EAAG;IAC/C,MACF,OAAO,CAAC,GACR,SAAS,kEAAkE,GAC3E,QAAQ,CAAC,GACT,KAAK,EAAE,CAAC;AAcH,IAAM,iBAA6B,MACxC,OAAO,CAAC,GACR,SAAS,kEAAkE,GAC3E,KAAK,EAAE,CAAC;AAKV,IAAM,YAAuC,CAAC,QAC5C,MAAM,MAAM,EAAE,GAAG,SAAS,GAAG,GAAG,KAAK,EAAE,CAAC;AAWnC,IAAM,SAAqB,UAChC,4DAA4D;AAKvD,IAAM,eAA2B,UACtC,4DAA4D;AAKvD,IAAM,YAAwB,UACnC,4DAA4D;AAgE9D,IAAM,gBAAyC,MAC7C,SAAS,kCAAkC,GAC3C,KAAK,EAAE,CAAC;AAGV,IAAM,qBAAqB,CAAC,WAAY,WAAY,WAAY,YAAY,SAAU;AACtF,SAAS,cAAc,KAAW;AAChC,QAAM,IAAI,OAAO;AACjB,MAAI,OAAO,MAAM,aAAc;AAC/B,WAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAClD,SAAM,KAAK,IAAK,OAAO;AAAG,aAAO,mBAAmB,CAAC;EACvD;AACA,SAAO;AACT;AAEA,SAAS,aAAa,QAAgB,OAAiB,gBAAgB,GAAC;AACtE,QAAM,MAAM,OAAO;AACnB,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,IAAI,OAAO,WAAW,CAAC;AAC7B,QAAI,IAAI,MAAM,IAAI;AAAK,YAAM,IAAI,MAAM,mBAAmB,MAAM,GAAG;AACnE,UAAM,cAAc,GAAG,IAAK,KAAK;EACnC;AACA,QAAM,cAAc,GAAG;AACvB,WAAS,IAAI,GAAG,IAAI,KAAK;AAAK,UAAM,cAAc,GAAG,IAAK,OAAO,WAAW,CAAC,IAAI;AACjF,WAAS,KAAK;AAAO,UAAM,cAAc,GAAG,IAAI;AAChD,WAAS,IAAI,GAAG,IAAI,GAAG;AAAK,UAAM,cAAc,GAAG;AACnD,SAAO;AACP,SAAO,cAAc,OAAO,cAAc,CAAC,MAAM,OAAO,EAAE,CAAE,GAAG,IAAI,GAAG,KAAK,CAAC;AAC9E;AAsBA,SAAS,UAAU,UAA8B;AAC/C,QAAM,iBAAiB,aAAa,WAAW,IAAI;AACnD,QAAM,SAAS,OAAO,CAAC;AACvB,QAAM,YAAY,OAAO;AACzB,QAAM,UAAU,OAAO;AACvB,QAAM,kBAAkB,cAAc,SAAS;AAE/C,WAAS,OACP,QACA,OACA,QAAwB,IAAE;AAE1B,SAAK,wBAAwB,MAAM;AACnC,QAAI,QAAQ,KAAK;AAAG,cAAQ,MAAM,KAAK,KAAK;AAC5C,YAAQ,iBAAiB,KAAK;AAC9B,UAAM,OAAO,OAAO;AACpB,QAAI,SAAS;AAAG,YAAM,IAAI,UAAU,yBAAyB,IAAI,EAAE;AACnE,UAAM,eAAe,OAAO,IAAI,MAAM;AACtC,QAAI,UAAU,SAAS,eAAe;AACpC,YAAM,IAAI,UAAU,UAAU,YAAY,kBAAkB,KAAK,EAAE;AACrE,UAAM,UAAU,OAAO,YAAW;AAClC,UAAM,MAAM,aAAa,SAAS,OAAO,cAAc;AACvD,WAAO,GAAG,OAAO,IAAI,cAAc,OAAO,KAAK,CAAC,GAAG,GAAG;EACxD;AAOA,WAAS,OAAO,KAAa,QAAwB,IAAE;AACrD,SAAK,uBAAuB,GAAG;AAC/B,UAAM,OAAO,IAAI;AACjB,QAAI,OAAO,KAAM,UAAU,SAAS,OAAO;AACzC,YAAM,IAAI,UAAU,0BAA0B,IAAI,KAAK,GAAG,mBAAmB,KAAK,GAAG;AAEvF,UAAM,UAAU,IAAI,YAAW;AAC/B,QAAI,QAAQ,WAAW,QAAQ,IAAI,YAAW;AAC5C,YAAM,IAAI,MAAM,uCAAuC;AACzD,UAAM,WAAW,QAAQ,YAAY,GAAG;AACxC,QAAI,aAAa,KAAK,aAAa;AACjC,YAAM,IAAI,MAAM,yDAAyD;AAC3E,UAAM,SAAS,QAAQ,MAAM,GAAG,QAAQ;AACxC,UAAM,OAAO,QAAQ,MAAM,WAAW,CAAC;AACvC,QAAI,KAAK,SAAS;AAAG,YAAM,IAAI,MAAM,yCAAyC;AAC9E,UAAM,QAAQ,cAAc,OAAO,IAAI,EAAE,MAAM,GAAG,EAAE;AACpD,UAAM,MAAM,aAAa,QAAQ,OAAO,cAAc;AACtD,QAAI,CAAC,KAAK,SAAS,GAAG;AAAG,YAAM,IAAI,MAAM,uBAAuB,GAAG,eAAe,GAAG,GAAG;AACxF,WAAO,EAAE,QAAQ,MAAK;EACxB;AAEA,QAAM,eAAe,cAAc,MAAM;AAEzC,WAAS,cAAc,KAAW;AAChC,UAAM,EAAE,QAAQ,MAAK,IAAK,OAAO,KAAK,KAAK;AAC3C,WAAO,EAAE,QAAQ,OAAO,OAAO,UAAU,KAAK,EAAC;EACjD;AAEA,WAAS,gBAAgB,QAAgB,OAAiB;AACxD,WAAO,OAAO,QAAQ,QAAQ,KAAK,CAAC;EACtC;AAEA,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;AAEJ;AAOO,IAAM,SAAiB,UAAU,QAAQ;AAQzC,IAAM,UAAkB,UAAU,SAAS;AAoBlD,IAAM,iBAA0C,MAC9C,OAAQ,WAAmB,KAAK,CAAA,CAAE,EAAE,UAAU,cAC9C,OAAQ,WAAmB,YAAY,YAAW;AAEpD,IAAM,aAAyB;EAC7B,OAAO,MAAI;AAAI,WAAO,IAAI;AAAG,WAAQ,KAAa,MAAK;EAAI;EAC3D,OAAO,GAAC;AAAI,SAAK,OAAO,CAAC;AAAG,WAAQ,WAAmB,QAAQ,CAAC;EAAG;;AAU9D,IAAM,MAAkB,gBAC3B,aACA,MACE,OAAO,CAAC,GACR,SAAS,kBAAkB,GAC3B,KAAK,EAAE,GACP,UAAU,CAAC,MAAa;AACtB,MAAI,OAAO,MAAM,YAAY,EAAE,SAAS,MAAM;AAC5C,UAAM,IAAI,UACR,oCAAoC,OAAO,CAAC,gBAAgB,EAAE,MAAM,EAAE;AAE1E,SAAO,EAAE,YAAW;AACtB,CAAC,CAAC;;;ACjzBD,IAAM,WAAW,CAAC,WAAuB,OAAO,OAAO,MAAM;AAC7D,IAAM,aAAa,CAAC,QAAgB,OAAO,OAAO,GAAG;;;ACHrD,SAAS,WAAWC,eAAkC;AAC5D,SAAO,WAAW,KAAK,KAAKA,aAAY,GAAG,CAAC,SAAS,KAAK,WAAW,CAAC,CAAC;AACxE;AAEA,IAAM,aAAa;AACZ,SAAS,SAAS,OAA2B;AAEnD,MAAI,MAAM,SAAS,YAAY;AAC9B,WAAO,KAAK,OAAO,aAAa,GAAG,KAAK,CAAC;EAC1C;AAEA,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,YAAY;AAClD,UAAMC,SAAQ,MAAM,MAAM,GAAG,IAAI,UAAU;AAC3C,cAAU,OAAO,aAAa,GAAGA,MAAK;EACvC;AAEA,SAAO,KAAK,MAAM;AACnB;;;AClBO,SAAS,QAAQ,QAA4B;AACnD,QAAM,aAAa,OAAO,WAAW,IAAI,IAAI,OAAO,MAAM,CAAC,IAAI;AAC/D,QAAM,SAAS,WAAW,SAAS,MAAM,IAAI,aAAa,IAAI,UAAU;AACxE,QAAM,SAAS,OAAO,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,SAAS,MAAM,EAAE,CAAC,KAAK,CAAC;AAEtF,MAAI,OAAO,WAAW,OAAO,SAAS,GAAG;AACxC,UAAM,IAAI,MAAM,sBAAsB,MAAM,EAAE;EAC/C;AAEA,SAAO,WAAW,KAAK,MAAM;AAC9B;AAEO,SAAS,MAAM,OAA2B;AAChD,SAAO,MAAM,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,GAAG,EAAE;AAChF;;;ACdO,SAAS,MAAS,OAAqB,MAAqB;AAClE,SAAO,MAAM,KAAK,EAAE,QAAQ,KAAK,KAAK,MAAM,SAAS,IAAI,EAAE,GAAG,CAAC,GAAG,MAAM;AACvE,WAAO,MAAM,MAAM,IAAI,OAAO,IAAI,KAAK,IAAI;EAC5C,CAAC;AACF;;;ACQO,IAAM,aAAN,MAA8B;EACpC,YAAY,aAA2C,SAAuC;AAC7F,QAAI,OAAO,gBAAgB,YAAY;AACtC,YAAM,IAAI;QACT,uHAC2D,WAAW;MACvE;IACD;AACA,SAAK,eAAe;AACpB,SAAK,gBAAgB,qBAAqB,OAAO;AACjD,SAAK,mBAAmB,wBAAwB,OAAO;AACvD,SAAK,cAAc,mBAAmB,OAAO;AAC7C,SAAK,YAAY,iBAAiB,OAAO;AACzC,SAAK,SAAS;AACd,SAAK,OAAO,aAAa,OAAO;EACjC;;;;EAaA,KAAK,KAAoB;AACxB,QAAI,QAAQ,QAAQ,QAAQ,QAAW;AACtC,YAAM,IAAI;QACT,oEAAoE,OAAO,GAAG,CAAC;MAChF;IACD;AAEA,UAAM,QAAQ,gBAAgB,IAAI;AAClC,UAAM,WAAW,KAAK;AACtB,QAAI;AAGJ,QAAI,UAAU;AACb,iBAAW,KAAK,YAAY,GAAG;AAC/B,YAAM,gBAAgB,SAAS,IAAI,QAAQ;AAC3C,UAAI,eAAe;AAClB,cAAM,YAAY,MAAM,cAAc,MAAM,YAAY,CAAC;AACzD,eAAO,IAAI,QAAQ,CAAC,YAAY;AAC/B,oBAAU,KAAK,MAAM;AACpB,oBAAQ,aAAa;UACtB,CAAC;QACF,CAAC;MACF;IACD;AAIA,UAAM,KAAK,KAAK,GAAG;AACnB,UAAM,UAAU,IAAI,QAAW,CAAC,SAAS,WAAW;AACnD,YAAM,UAAU,KAAK,EAAE,SAAS,OAAO,CAAC;IACzC,CAAC;AAGD,QAAI,UAAU;AACb,eAAS,IAAI,UAAW,OAAO;IAChC;AAEA,WAAO;EACR;;;;;;;;;;;;;;;;;;;;;EAsBA,SAAS,MAAmD;AAC3D,QAAI,CAAC,YAAY,IAAI,GAAG;AACvB,YAAM,IAAI;QACT,2EAA2E,IAAI;MAChF;IACD;AAEA,UAAM,eAAe,CAAC;AACtB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACrC,mBAAa,KAAK,KAAK,KAAK,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,UAAU,KAAK,CAAC;IAC7D;AACA,WAAO,QAAQ,IAAI,YAAY;EAChC;;;;;EAMA,MAAM,KAAc;AACnB,UAAM,WAAW,KAAK;AACtB,QAAI,UAAU;AACb,YAAM,WAAW,KAAK,YAAY,GAAG;AACrC,eAAS,OAAO,QAAQ;IACzB;AACA,WAAO;EACR;;;;;;EAOA,WAAiB;AAChB,UAAM,WAAW,KAAK;AACtB,QAAI,UAAU;AACb,eAAS,MAAM;IAChB;AACA,WAAO;EACR;;;;;;;EAQA,MAAM,KAAQ,OAAqC;AAClD,UAAM,WAAW,KAAK;AACtB,QAAI,UAAU;AACb,YAAM,WAAW,KAAK,YAAY,GAAG;AAGrC,UAAI,SAAS,IAAI,QAAQ,MAAM,QAAW;AAGzC,YAAI;AACJ,YAAI,iBAAiB,OAAO;AAC3B,oBAAU,QAAQ,OAAO,KAAK;AAG9B,kBAAQ,MAAM,MAAM;UAAC,CAAC;QACvB,OAAO;AACN,oBAAU,QAAQ,QAAQ,KAAK;QAChC;AACA,iBAAS,IAAI,UAAU,OAAO;MAC/B;IACD;AACA,WAAO;EACR;AAQD;AA4BA,IAAM;;EAEL,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa,aACxD,SAAU,IAAI;AACd,QAAI,CAAC,iBAAiB;AACrB,wBAAkB,QAAQ,QAAQ;IACnC;AACA,oBAAgB,KAAK,MAAM;AAE1B,cAAQ,SAAS,EAAE;IACpB,CAAC;EACF;;IAEA,OAAO,iBAAiB,aACtB,SAAU,IAAI;AAEd,mBAAa,EAAE;IAChB,IACC,SAAU,IAAI;AACd,iBAAW,EAAE;IACd;;;AAGJ,IAAI;AAeJ,SAAS,gBAAsB,QAA4C;AAG1E,QAAM,gBAAgB,OAAO;AAC7B,MACC,kBAAkB,QAClB,CAAC,cAAc,iBACf,cAAc,KAAK,SAAS,OAAO,eAClC;AACD,WAAO;EACR;AAGA,QAAM,WAAW,EAAE,eAAe,OAAO,MAAM,CAAC,GAAG,WAAW,CAAC,EAAE;AAGjE,SAAO,SAAS;AAGhB,SAAO,iBAAiB,MAAM;AAC7B,kBAAc,QAAQ,QAAQ;EAC/B,CAAC;AAED,SAAO;AACR;AAEA,SAAS,cAAoB,QAA+B,OAAoB;AAE/E,QAAM,gBAAgB;AAGtB,MAAI,MAAM,KAAK,WAAW,GAAG;AAC5B,qBAAiB,KAAK;AACtB;EACD;AAIA,MAAI;AACJ,MAAI;AACH,mBAAe,OAAO,aAAa,MAAM,IAAI;EAC9C,SAAS,GAAG;AACX,WAAO;MACN;MACA;MACA,IAAI;QACH,sJAE2B,OAAO,CAAC,CAAC;MACrC;IACD;EACD;AAGA,MAAI,CAAC,gBAAgB,OAAO,aAAa,SAAS,YAAY;AAC7D,WAAO;MACN;MACA;MACA,IAAI;QACH,yJAE0B,OAAO,YAAY,CAAC;MAC/C;IACD;EACD;AAGA,UAAQ,QAAQ,YAAY,EAC1B,KAAK,CAAC,WAAW;AAEjB,QAAI,CAAC,YAAY,MAAM,GAAG;AACzB,YAAM,IAAI;QACT,qKAEsC,OAAO,MAAM,CAAC;MACrD;IACD;AACA,QAAI,OAAO,WAAW,MAAM,KAAK,QAAQ;AACxC,YAAM,IAAI;QACT;;;EAIe,OAAO,MAAM,IAAI,CAAC;;;EAChB,OAAO,MAAM,CAAC;MAChC;IACD;AAGA,qBAAiB,KAAK;AAGtB,aAAS,IAAI,GAAG,IAAI,MAAM,UAAU,QAAQ,KAAK;AAChD,YAAM,QAAQ,OAAO,CAAC;AACtB,UAAI,iBAAiB,OAAO;AAC3B,cAAM,UAAU,CAAC,EAAE,OAAO,KAAK;MAChC,OAAO;AACN,cAAM,UAAU,CAAC,EAAE,QAAQ,KAAK;MACjC;IACD;EACD,CAAC,EACA,MAAM,CAAC,UAAmB;AAC1B,mBAAe,QAAQ,OAAO,KAAc;EAC7C,CAAC;AACH;AAIA,SAAS,eAAqB,QAA+B,OAAoB,OAAc;AAE9F,mBAAiB,KAAK;AACtB,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AAC3C,WAAO,MAAM,MAAM,KAAK,CAAC,CAAC;AAC1B,UAAM,UAAU,CAAC,EAAE,OAAO,KAAK;EAChC;AACD;AAGA,SAAS,iBAAiB,OAAwB;AACjD,MAAI,MAAM,WAAW;AACpB,aAAS,IAAI,GAAG,IAAI,MAAM,UAAU,QAAQ,KAAK;AAChD,YAAM,UAAU,CAAC,EAAE;IACpB;EACD;AACD;AAGA,SAAS,qBAA8B,SAA+C;AACrF,QAAM,cAAc,CAAC,WAAW,QAAQ,UAAU;AAClD,MAAI,CAAC,aAAa;AACjB,WAAO;EACR;AACA,QAAM,eAAe,WAAW,QAAQ;AACxC,MAAI,iBAAiB,QAAW;AAC/B,WAAO;EACR;AACA,MAAI,OAAO,iBAAiB,YAAY,eAAe,GAAG;AACzD,UAAM,IAAI,UAAU,2CAA2C,YAAY,EAAE;EAC9E;AACA,SAAO;AACR;AAGA,SAAS,wBACR,SAC2B;AAC3B,QAAM,kBAAkB,WAAW,QAAQ;AAC3C,MAAI,oBAAoB,QAAW;AAClC,WAAO;EACR;AACA,MAAI,OAAO,oBAAoB,YAAY;AAC1C,UAAM,IAAI,UAAU,uCAAuC,eAAe,EAAE;EAC7E;AACA,SAAO;AACR;AAGA,SAAS,mBAA4B,SAAsD;AAC1F,QAAM,aAAa,WAAW,QAAQ;AACtC,MAAI,eAAe,QAAW;AAC7B,WAAO,CAAC,QAAW;EACpB;AACA,MAAI,OAAO,eAAe,YAAY;AACrC,UAAM,IAAI,UAAU,kCAAkC,UAAU,EAAE;EACnE;AACA,SAAO;AACR;AAGA,SAAS,iBACR,SAC4C;AAC5C,QAAM,cAAc,CAAC,WAAW,QAAQ,UAAU;AAClD,MAAI,CAAC,aAAa;AACjB,WAAO;EACR;AACA,QAAM,WAAW,WAAW,QAAQ;AACpC,MAAI,aAAa,QAAW;AAC3B,WAAO,oBAAI,IAAI;EAChB;AACA,MAAI,aAAa,MAAM;AACtB,UAAM,iBAAiB,CAAC,OAAO,OAAO,UAAU,OAAO;AACvD,UAAM,mBAAmB,eAAe;MACvC,CAAC,WAAW,YAAY,OAAO,SAAS,MAAM,MAAM;IACrD;AACA,QAAI,iBAAiB,WAAW,GAAG;AAClC,YAAM,IAAI,UAAU,sCAAsC,iBAAiB,KAAK,IAAI,CAAC;IACtF;EACD;AACA,SAAO;AACR;AAEA,SAAS,aAAsB,SAAsD;AACpF,MAAI,WAAW,QAAQ,MAAM;AAC5B,WAAO,QAAQ;EAChB;AAEA,SAAO;AACR;AAEA,SAAS,YAAY,GAAqC;AACzD,SACC,OAAO,MAAM,YACb,MAAM,QACN,YAAY,KACZ,OAAO,EAAE,WAAW,aACnB,EAAE,WAAW,KAAM,EAAE,SAAS,KAAK,OAAO,UAAU,eAAe,KAAK,GAAG,EAAE,SAAS,CAAC;AAE1F;;;AChcO,SAAS,WAAW,KAAuB;AACjD,QAAM,MAAM,CAAC;AACb,MAAI,MAAM;AAEV,MAAI,QAAQ,GAAG;AACd,WAAO,CAAC,CAAC;EACV;AAEA,SAAO,MAAM,GAAG;AACf,QAAI,GAAG,IAAI,MAAM;AACjB,QAAK,QAAQ,GAAI;AAChB,UAAI,GAAG,KAAK;IACb;AACA,WAAO;EACR;AAEA,SAAO;AACR;AAIO,SAAS,WAAW,KAGzB;AACD,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,MAAI,MAAM;AAGV,SAAO,MAAM;AACZ,UAAM,OAAO,IAAI,GAAG;AACpB,WAAO;AACP,cAAU,OAAO,QAAS;AAC1B,SAAK,OAAO,SAAU,GAAG;AACxB;IACD;AACA,aAAS;EACV;AAEA,SAAO;IACN,OAAO;IACP,QAAQ;EACT;AACD;;;ACdO,IAAM,YAAN,MAAgB;;;;EAOtB,YAAY,MAAkB;AAL9B,SAAQ,eAAuB;AAM9B,SAAK,WAAW,IAAI,SAAS,KAAK,QAAQ,KAAK,YAAY,KAAK,UAAU;EAC3E;;;;;;;EAOA,MAAM,OAAe;AACpB,SAAK,gBAAgB;AACrB,WAAO;EACR;;;;;EAKA,QAAgB;AACf,UAAM,QAAQ,KAAK,SAAS,SAAS,KAAK,YAAY;AACtD,SAAK,MAAM,CAAC;AACZ,WAAO;EACR;;;;;EAKA,SAAiB;AAChB,UAAM,QAAQ,KAAK,SAAS,UAAU,KAAK,cAAc,IAAI;AAC7D,SAAK,MAAM,CAAC;AACZ,WAAO;EACR;;;;;EAKA,SAAiB;AAChB,UAAM,QAAQ,KAAK,SAAS,UAAU,KAAK,cAAc,IAAI;AAC7D,SAAK,MAAM,CAAC;AACZ,WAAO;EACR;;;;;EAKA,SAAiB;AAChB,UAAM,SAAS,KAAK,OAAO;AAC3B,UAAM,SAAS,KAAK,OAAO;AAE3B,UAAM,SAAS,OAAO,SAAS,EAAE,IAAI,OAAO,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AAExE,WAAO,OAAO,OAAO,MAAM,EAAE,SAAS,EAAE;EACzC;;;;EAIA,UAAkB;AACjB,UAAM,SAAS,OAAO,KAAK,OAAO,CAAC;AACnC,UAAM,SAAS,OAAO,KAAK,OAAO,CAAC;AACnC,UAAM,SAAS,OAAO,SAAS,EAAE,IAAI,OAAO,SAAS,EAAE,EAAE,SAAS,IAAI,GAAG;AAEzE,WAAO,OAAO,OAAO,MAAM,EAAE,SAAS,EAAE;EACzC;;;;;EAKA,UAAkB;AACjB,UAAM,SAAS,OAAO,KAAK,QAAQ,CAAC;AACpC,UAAM,SAAS,OAAO,KAAK,QAAQ,CAAC;AACpC,UAAM,SAAS,OAAO,SAAS,EAAE,IAAI,OAAO,SAAS,EAAE,EAAE,SAAS,IAAI,GAAG;AAEzE,WAAO,OAAO,OAAO,MAAM,EAAE,SAAS,EAAE;EACzC;;;;;EAKA,UAAU,KAAyB;AAClC,UAAM,QAAQ,KAAK,eAAe,KAAK,SAAS;AAChD,UAAM,QAAQ,IAAI,WAAW,KAAK,SAAS,QAAQ,OAAO,GAAG;AAE7D,SAAK,MAAM,GAAG;AAEd,WAAO;EACR;;;;;;EAMA,WAAmB;AAClB,UAAM,QAAQ,KAAK,eAAe,KAAK,SAAS;AAChD,UAAM,SAAS,IAAI,WAAW,KAAK,SAAS,QAAQ,KAAK;AACzD,UAAM,EAAE,OAAO,OAAO,IAAI,WAAW,MAAM;AAE3C,SAAK,MAAM,MAAM;AAEjB,WAAO;EACR;;;;;;;EAOA,QAAQ,IAAkE;AACzE,UAAM,SAAS,KAAK,SAAS;AAC7B,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,aAAO,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC;IAChC;AACA,WAAO;EACR;AACD;;;AC9IO,SAAS,UAAU,MAAkB,UAA4B;AACvE,UAAQ,UAAU;IACjB,KAAK;AACJ,aAAO,SAAS,IAAI;IACrB,KAAK;AACJ,aAAO,SAAS,IAAI;IACrB,KAAK;AACJ,aAAO,MAAM,IAAI;IAClB;AACC,YAAM,IAAI,MAAM,yDAAyD;EAC3E;AACD;AAsBO,SAAS,uBACf,KACA,oBAAsC,CAAC,KAAK,GAAG,GAC9C;AACD,QAAM,CAAC,MAAM,KAAK,IAAI;AACtB,QAAM,MAAM,CAAC;AACb,MAAI,OAAO;AACX,MAAI,sBAAsB;AAE1B,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACpC,UAAM,OAAO,IAAI,CAAC;AAClB,QAAI,SAAS,MAAM;AAClB;IACD;AACA,QAAI,SAAS,OAAO;AACnB;IACD;AACA,QAAI,wBAAwB,KAAK,SAAS,KAAK;AAC9C,UAAI,KAAK,KAAK,KAAK,CAAC;AACpB,aAAO;AACP;IACD;AACA,YAAQ;EACT;AAEA,MAAI,KAAK,KAAK,KAAK,CAAC;AAEpB,SAAO;AACR;;;ACxCO,IAAM,YAAN,MAAgB;EAOtB,YAAY;IACX,cAAc;IACd,UAAU;IACV,eAAe;EAChB,IAAsB,CAAC,GAAG;AAT1B,SAAQ,eAAuB;AAU9B,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,WAAW,IAAI,SAAS,IAAI,YAAY,WAAW,CAAC;EAC1D;EAEQ,iBAAiB,OAAe;AACvC,UAAM,eAAe,KAAK,eAAe;AACzC,QAAI,eAAe,KAAK,MAAM;AAC7B,YAAM,WAAW,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO,KAAK,YAAY;AACrE,UAAI,eAAe,UAAU;AAC5B,cAAM,IAAI;UACT,yFAAyF,KAAK,IAAI,eAAe,KAAK,OAAO,oBAAoB,YAAY;QAC9J;MACD;AAEA,WAAK,OAAO;AACZ,YAAM,aAAa,IAAI,YAAY,KAAK,IAAI;AAC5C,UAAI,WAAW,UAAU,EAAE,IAAI,IAAI,WAAW,KAAK,SAAS,MAAM,CAAC;AACnE,WAAK,WAAW,IAAI,SAAS,UAAU;IACxC;EACD;;;;;;;EAQA,MAAM,OAAqB;AAC1B,SAAK,gBAAgB;AACrB,WAAO;EACR;;;;;;EAMA,OAAO,OAA8B;AACpC,SAAK,iBAAiB,CAAC;AACvB,SAAK,SAAS,SAAS,KAAK,cAAc,OAAO,KAAK,CAAC;AACvD,WAAO,KAAK,MAAM,CAAC;EACpB;;;;;;EAMA,QAAQ,OAA8B;AACrC,SAAK,iBAAiB,CAAC;AACvB,SAAK,SAAS,UAAU,KAAK,cAAc,OAAO,KAAK,GAAG,IAAI;AAC9D,WAAO,KAAK,MAAM,CAAC;EACpB;;;;;;EAMA,QAAQ,OAA8B;AACrC,SAAK,iBAAiB,CAAC;AACvB,SAAK,SAAS,UAAU,KAAK,cAAc,OAAO,KAAK,GAAG,IAAI;AAC9D,WAAO,KAAK,MAAM,CAAC;EACpB;;;;;;EAMA,QAAQ,OAA8B;AACrC,mBAAe,OAAO,KAAK,GAAG,CAAC,EAAE,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;AAEhE,WAAO;EACR;;;;;;;EAOA,SAAS,OAA8B;AACtC,mBAAe,OAAO,KAAK,GAAG,EAAE,EAAE,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;AAEjE,WAAO;EACR;;;;;;;EAOA,SAAS,OAA8B;AACtC,mBAAe,OAAO,KAAK,GAAG,EAAE,EAAE,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;AAEjE,WAAO;EACR;;;;;;;EAOA,UAAU,OAAqB;AAC9B,eAAW,KAAK,EAAE,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;AACjD,WAAO;EACR;;;;;;;;;EASA,SAAS,QAAe,IAAwE;AAC/F,SAAK,UAAU,OAAO,MAAM;AAC5B,UAAM,KAAK,MAAM,EAAE,QAAQ,CAAC,IAAI,MAAM,GAAG,MAAM,IAAI,GAAG,OAAO,MAAM,CAAC;AACpE,WAAO;EACR;;;;;EAMA,EAAE,OAAO,QAAQ,IAAwC;AACxD,aAAS,IAAI,GAAG,IAAI,KAAK,cAAc,KAAK;AAC3C,YAAM,KAAK,SAAS,SAAS,CAAC;IAC/B;AACA,WAAO,KAAK,QAAQ;EACrB;;;;;EAMA,UAAsB;AACrB,WAAO,IAAI,WAAW,KAAK,SAAS,OAAO,MAAM,GAAG,KAAK,YAAY,CAAC;EACvE;;;;;EAMA,SAAS,UAA4B;AACpC,WAAO,UAAU,KAAK,QAAQ,GAAG,QAAQ;EAC1C;AACD;AAEA,SAAS,eAAe,QAAgB,MAAc;AACrD,QAAM,SAAS,IAAI,WAAW,IAAI;AAClC,MAAI,IAAI;AACR,SAAO,SAAS,GAAG;AAClB,WAAO,CAAC,IAAI,OAAO,SAAS,OAAO,GAAG,CAAC;AACvC,aAAS,SAAS,OAAO,GAAG;AAC5B,SAAK;EACN;AACA,SAAO;AACR;;;;;;;;;;AC1MA,IAAA;AAAA,IAAA;AAAA,IAAA;AAAA,IAAA;AAcO,IAAM,WAAN,MAAMC,UAAsB;EAUlC,YACC,SAQC;AAZF,iBAAA,MAAA,MAAA;AACA,iBAAA,MAAA,UAAA;AAYC,SAAK,OAAO,QAAQ;AACpB,SAAK,OAAO,QAAQ;AACpB,SAAK,iBAAiB,QAAQ,mBAAmB,MAAM;AACvD,iBAAA,MAAK,QAAS,QAAQ,KAAA;AACtB,iBAAA,MAAK,YACJ,QAAQ,cACP,CAAC,OAAOC,aAAY;AACpB,YAAM,SAAS,IAAI,UAAU;QAC5B,aAAa,KAAK,eAAe,KAAK,KAAK;QAC3C,GAAGA;MACJ,CAAC;AACD,mBAAA,MAAK,MAAA,EAAL,KAAA,MAAY,OAAO,MAAA;AACnB,aAAO,OAAO,QAAQ;IACvB,EAAA;AAED,SAAK,WAAW,QAAQ,aAAa,MAAM;IAAC;EAC7C;EAEA,MAAM,OAAc,QAAmB;AACtC,SAAK,SAAS,KAAK;AACnB,iBAAA,MAAK,MAAA,EAAL,KAAA,MAAY,OAAO,MAAA;EACpB;EAEA,UAAU,OAAc,SAA4B;AACnD,SAAK,SAAS,KAAK;AACnB,WAAO,IAAI,cAAc,MAAM,aAAA,MAAK,UAAA,EAAL,KAAA,MAAgB,OAAO,OAAA,CAAQ;EAC/D;EAEA,MAAM,OAAsB;AAC3B,UAAM,SAAS,IAAI,UAAU,KAAK;AAClC,WAAO,KAAK,KAAK,MAAM;EACxB;EAEA,QAAQC,MAAa;AACpB,WAAO,KAAK,MAAM,QAAQA,IAAG,CAAC;EAC/B;EAEA,WAAW,KAAa;AACvB,WAAO,KAAK,MAAM,WAAW,GAAG,CAAC;EAClC;EAEA,WAAW,KAAa;AACvB,WAAO,KAAK,MAAM,WAAW,GAAG,CAAC;EAClC;EAEA,UAAkC;IACjC;IACA;IACA;IACA;EACD,GAGgC;AAC/B,WAAO,IAAIF,UAAoB;MAC9B,MAAM,QAAQ,KAAK;MACnB,MAAM,CAAC,WAAY,SAAS,OAAO,KAAK,KAAK,MAAM,CAAC,IAAK,KAAK,KAAK,MAAM;MACzE,OAAO,CAAC,OAAO,WAAW,aAAA,MAAK,MAAA,EAAL,KAAA,MAAY,QAAQ,MAAM,KAAK,IAAK,OAAiB,MAAA;MAC/E,gBAAgB,CAAC,UAAU,KAAK,eAAe,QAAQ,MAAM,KAAK,IAAK,KAAe;MACtF,WAAW,CAAC,OAAO,YAClB,aAAA,MAAK,UAAA,EAAL,KAAA,MAAgB,QAAQ,MAAM,KAAK,IAAK,OAAiB,OAAA;MAC1D,UAAU,CAAC,UAAU;AACpB,mBAAW,KAAK;AAChB,aAAK,SAAS,QAAQ,MAAM,KAAK,IAAK,KAAe;MACtD;IACD,CAAC;EACF;AACD;AAhFC,SAAA,oBAAA,QAAA;AACA,aAAA,oBAAA,QAAA;AARM,IAAM,UAAN;AAyFP,IAAM,uBAAuB,OAAO,IAAI,wBAAwB;AAKzD,IAAM,gBAAN,MAAkC;EAUxC,YAAY,MAAyB,QAAoB;AATzD,iBAAA,MAAA,OAAA;AACA,iBAAA,MAAA,MAAA;AASC,iBAAA,MAAK,SAAU,IAAA;AACf,iBAAA,MAAK,QAAS,MAAA;EACf;;;EAPA,KAAK,oBAAoB,IAAI;AAC5B,WAAO;EACR;EAOA,UAAU;AACT,WAAO,aAAA,MAAK,MAAA;EACb;EAEA,QAAQ;AACP,WAAO,MAAM,aAAA,MAAK,MAAA,CAAM;EACzB;EAEA,WAAW;AACV,WAAO,SAAS,aAAA,MAAK,MAAA,CAAM;EAC5B;EAEA,WAAW;AACV,WAAO,SAAS,aAAA,MAAK,MAAA,CAAM;EAC5B;EAEA,QAAQ;AACP,WAAO,aAAA,MAAK,OAAA,EAAQ,MAAM,aAAA,MAAK,MAAA,CAAM;EACtC;AACD;AAjCC,UAAA,oBAAA,QAAA;AACA,SAAA,oBAAA,QAAA;AAkCM,SAAS,iBAA+B;EAC9C;EACA,GAAG;AACJ,GAK8B;AAC7B,SAAO,IAAI,QAAkB;IAC5B,GAAG;IACH,gBAAgB,MAAM;EACvB,CAAC;AACF;AAEO,SAAS,YAAY;EAC3B;EACA;EACA,GAAG;AACJ,GAMoC;AACnC,SAAO,iBAAyB;IAC/B,GAAG;IACH,MAAM,CAAC,WAAW,OAAO,UAAU,EAAE;IACrC,OAAO,CAAC,OAAO,WAAW,OAAO,WAAW,EAAE,KAAK;IACnD,UAAU,CAAC,UAAU;AACpB,UAAI,QAAQ,KAAK,QAAQ,QAAQ,UAAU;AAC1C,cAAM,IAAI;UACT,WAAW,QAAQ,IAAI,WAAW,KAAK,+BAA+B,QAAQ,QAAQ;QACvF;MACD;AACA,cAAQ,WAAW,KAAK;IACzB;EACD,CAAC;AACF;AAEO,SAAS,eAAe;EAC9B;EACA;EACA,GAAG;AACJ,GAMsD;AACrD,SAAO,iBAAmD;IACzD,GAAG;IACH,MAAM,CAAC,WAAW,OAAO,UAAU,EAAE;IACrC,OAAO,CAAC,OAAO,WAAW,OAAO,WAAW,EAAE,OAAO,KAAK,CAAC;IAC3D,UAAU,CAAC,QAAQ;AAClB,YAAM,QAAQ,OAAO,GAAG;AACxB,UAAI,QAAQ,KAAK,QAAQ,QAAQ,UAAU;AAC1C,cAAM,IAAI;UACT,WAAW,QAAQ,IAAI,WAAW,KAAK,+BAA+B,QAAQ,QAAQ;QACvF;MACD;AACA,cAAQ,WAAW,KAAK;IACzB;EACD,CAAC;AACF;AAEO,SAAS,mBAAiC;EAChD;EACA,GAAG;AACJ,GAI8B;AAC7B,QAAM,OAAO,IAAI,QAAkB;IAClC,GAAG;IACH;IACA,OAAO,CAAC,OAAO,WAAW;AACzB,iBAAW,QAAQ,KAAK,UAAU,KAAK,EAAE,QAAQ,GAAG;AACnD,eAAO,OAAO,IAAI;MACnB;IACD;EACD,CAAC;AAED,SAAO;AACR;AAEO,SAAS,kBAAkB;EACjC,SAAAG;EACA;EACA,GAAG;AACJ,GAK4B;AAC3B,SAAO,IAAI,QAAgB;IAC1B,GAAG;IACH,MAAM,CAAC,WAAW;AACjB,YAAM,SAAS,OAAO,SAAS;AAC/B,YAAM,QAAQ,OAAO,UAAU,MAAM;AAErC,aAAO,UAAU,KAAK;IACvB;IACA,OAAO,CAACC,MAAK,WAAW;AACvB,YAAM,QAAQD,SAAQC,IAAG;AACzB,aAAO,UAAU,MAAM,MAAM;AAC7B,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,eAAO,OAAO,MAAM,CAAC,CAAC;MACvB;IACD;IACA,WAAW,CAAC,UAAU;AACrB,YAAM,QAAQD,SAAQ,KAAK;AAC3B,YAAM,OAAO,WAAW,MAAM,MAAM;AACpC,YAAM,SAAS,IAAI,WAAW,KAAK,SAAS,MAAM,MAAM;AACxD,aAAO,IAAI,MAAM,CAAC;AAClB,aAAO,IAAI,OAAO,KAAK,MAAM;AAE7B,aAAO;IACR;IACA,UAAU,CAAC,UAAU;AACpB,UAAI,OAAO,UAAU,UAAU;AAC9B,cAAM,IAAI,UAAU,WAAW,QAAQ,IAAI,WAAW,KAAK,mBAAmB;MAC/E;AACA,cAAQ,WAAW,KAAK;IACzB;EACD,CAAC;AACF;AAEO,SAAS,YAAsB,IAA6B;AAClE,MAAI,WAAqC;AACzC,WAAS,UAAU;AAClB,QAAI,CAAC,UAAU;AACd,iBAAW,GAAG;IACf;AACA,WAAO;EACR;AAEA,SAAO,IAAI,QAAkB;IAC5B,MAAM;IACN,MAAM,CAAC,SAAS,QAAQ,EAAE,KAAK,IAAI;IACnC,gBAAgB,CAAC,UAAU,QAAQ,EAAE,eAAe,KAAK;IACzD,OAAO,CAAC,OAAO,WAAW,QAAQ,EAAE,MAAM,OAAO,MAAM;IACvD,WAAW,CAAC,OAAO,YAAY,QAAQ,EAAE,UAAU,OAAO,OAAO,EAAE,QAAQ;EAC5E,CAAC;AACF;;;ACpRO,IAAM,MAAM;;;;;;EAMlB,GAAG,SAAkC;AACpC,WAAO,YAAY;MAClB,MAAM;MACN,YAAY;MACZ,aAAa;MACb,MAAM;MACN,UAAU,KAAK,IAAI;MACnB,GAAG;IACJ,CAAC;EACF;;;;;;EAOA,IAAI,SAAkC;AACrC,WAAO,YAAY;MAClB,MAAM;MACN,YAAY;MACZ,aAAa;MACb,MAAM;MACN,UAAU,KAAK,KAAK;MACpB,GAAG;IACJ,CAAC;EACF;;;;;;EAOA,IAAI,SAAkC;AACrC,WAAO,YAAY;MAClB,MAAM;MACN,YAAY;MACZ,aAAa;MACb,MAAM;MACN,UAAU,KAAK,KAAK;MACpB,GAAG;IACJ,CAAC;EACF;;;;;;EAOA,IAAI,SAA4D;AAC/D,WAAO,eAAe;MACrB,MAAM;MACN,YAAY;MACZ,aAAa;MACb,MAAM;MACN,UAAU,MAAM,MAAM;MACtB,GAAG;IACJ,CAAC;EACF;;;;;;EAOA,KAAK,SAA4D;AAChE,WAAO,eAAe;MACrB,MAAM;MACN,YAAY;MACZ,aAAa;MACb,MAAM;MACN,UAAU,MAAM,OAAO;MACvB,GAAG;IACJ,CAAC;EACF;;;;;;EAOA,KAAK,SAA4D;AAChE,WAAO,eAAe;MACrB,MAAM;MACN,YAAY;MACZ,aAAa;MACb,MAAM;MACN,UAAU,MAAM,OAAO;MACvB,GAAG;IACJ,CAAC;EACF;;;;;;EAOA,KAAK,SAAmC;AACvC,WAAO,iBAA0B;MAChC,MAAM;MACN,MAAM;MACN,MAAM,CAAC,WAAW,OAAO,MAAM,MAAM;MACrC,OAAO,CAAC,OAAO,WAAW,OAAO,OAAO,QAAQ,IAAI,CAAC;MACrD,GAAG;MACH,UAAU,CAAC,UAAU;AACpB,iBAAS,WAAW,KAAK;AACzB,YAAI,OAAO,UAAU,WAAW;AAC/B,gBAAM,IAAI,UAAU,2BAA2B,OAAO,KAAK,EAAE;QAC9D;MACD;IACD,CAAC;EACF;;;;;;EAOA,QAAQ,SAAkC;AACzC,WAAO,mBAA2B;MACjC,MAAM;MACN,MAAM,CAAC,WAAW,OAAO,SAAS;MAClC,WAAW,CAAC,UAAU;AACrB,eAAO,WAAW,KAAK,WAAW,KAAK,CAAC;MACzC;MACA,GAAG;IACJ,CAAC;EACF;;;;;;;EAQA,MAAwB,MAAS,SAAwD;AACxF,WAAO,iBAA+C;MACrD,MAAM,SAAS,IAAI;MACnB;MACA,MAAM,CAAC,WAAW,OAAO,UAAU,IAAI;MACvC,OAAO,CAAC,OAAO,WAAW;AACzB,cAAM,QAAQ,IAAI,WAAW,KAAK;AAClC,iBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC9B,iBAAO,OAAO,MAAM,CAAC,KAAK,CAAC;QAC5B;MACD;MACA,GAAG;MACH,UAAU,CAAC,UAAU;AACpB,iBAAS,WAAW,KAAK;AACzB,YAAI,CAAC,SAAS,OAAO,UAAU,YAAY,EAAE,YAAY,QAAQ;AAChE,gBAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK,EAAE;QAC5D;AACA,YAAI,MAAM,WAAW,MAAM;AAC1B,gBAAM,IAAI,UAAU,4BAA4B,IAAI,WAAW,MAAM,MAAM,EAAE;QAC9E;MACD;IACD,CAAC;EACF;;;;;;;EAQA,WAAW,SAAwD;AAClE,WAAO,IAAI,QAAsC;MAChD,MAAM;MACN,MAAM,CAAC,WAAW;AACjB,cAAM,SAAS,OAAO,SAAS;AAE/B,eAAO,OAAO,UAAU,MAAM;MAC/B;MACA,OAAO,CAAC,OAAO,WAAW;AACzB,cAAM,QAAQ,IAAI,WAAW,KAAK;AAClC,eAAO,UAAU,MAAM,MAAM;AAC7B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,iBAAO,OAAO,MAAM,CAAC,KAAK,CAAC;QAC5B;MACD;MACA,GAAG;MACH,gBAAgB,CAAC,UAAU;AAC1B,cAAM,SAAS,YAAY,QAAS,MAAM,SAAoB;AAC9D,eAAO,UAAU,OAAO,OAAO,WAAW,MAAM,EAAE,SAAS;MAC5D;MACA,UAAU,CAAC,UAAU;AACpB,iBAAS,WAAW,KAAK;AACzB,YAAI,CAAC,SAAS,OAAO,UAAU,YAAY,EAAE,YAAY,QAAQ;AAChE,gBAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK,EAAE;QAC5D;MACD;IACD,CAAC;EACF;;;;;;EAOA,OAAO,SAAkC;AACxC,WAAO,kBAAkB;MACxB,MAAM;MACN,SAAS,CAAC,UAAU,IAAI,YAAY,EAAE,OAAO,KAAK;MAClD,WAAW,CAAC,UAAU,IAAI,YAAY,EAAE,OAAO,KAAK;MACpD,GAAG;IACJ,CAAC;EACF;;;;;;;;EASA,WACC,MACA,MACA,SACC;AACD,WAAO,IAAI,QAAmD;MAC7D,MAAM,GAAG,KAAK,IAAI,IAAI,IAAI;MAC1B,MAAM,CAAC,WAAW;AACjB,cAAM,SAAc,IAAI,MAAM,IAAI;AAClC,iBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC9B,iBAAO,CAAC,IAAI,KAAK,KAAK,MAAM;QAC7B;AACA,eAAO;MACR;MACA,OAAO,CAAC,OAAO,WAAW;AACzB,mBAAW,QAAQ,OAAO;AACzB,eAAK,MAAM,MAAM,MAAM;QACxB;MACD;MACA,GAAG;MACH,UAAU,CAAC,UAAU;AACpB,iBAAS,WAAW,KAAK;AACzB,YAAI,CAAC,SAAS,OAAO,UAAU,YAAY,EAAE,YAAY,QAAQ;AAChE,gBAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK,EAAE;QAC5D;AACA,YAAI,MAAM,WAAW,MAAM;AAC1B,gBAAM,IAAI,UAAU,4BAA4B,IAAI,WAAW,MAAM,MAAM,EAAE;QAC9E;MACD;IACD,CAAC;EACF;;;;;;;;EASA,OAAiB,MAAyB;AACzC,WAAO,IACL,KAAK,UAAU,KAAK,IAAI,KAAK;MAC7B,MAAM;MACN,MAAM;IACP,CAAC,EACA,UAAU;MACV,OAAO,CAAC,UAAoC;AAC3C,YAAI,SAAS,MAAM;AAClB,iBAAO,EAAE,MAAM,KAAK;QACrB;AAEA,eAAO,EAAE,MAAM,MAAM;MACtB;MACA,QAAQ,CAAC,UAAU;AAClB,YAAI,MAAM,UAAU,QAAQ;AAC3B,iBAAO,MAAM;QACd;AAEA,eAAO;MACR;IACD,CAAC;EACH;;;;;;;;EASA,OACC,MACA,SACC;AACD,WAAO,IAAI,QAAmD;MAC7D,MAAM,UAAU,KAAK,IAAI;MACzB,MAAM,CAAC,WAAW;AACjB,cAAM,SAAS,OAAO,SAAS;AAC/B,cAAM,SAAc,IAAI,MAAM,MAAM;AACpC,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,iBAAO,CAAC,IAAI,KAAK,KAAK,MAAM;QAC7B;AACA,eAAO;MACR;MACA,OAAO,CAAC,OAAO,WAAW;AACzB,eAAO,UAAU,MAAM,MAAM;AAC7B,mBAAW,QAAQ,OAAO;AACzB,eAAK,MAAM,MAAM,MAAM;QACxB;MACD;MACA,GAAG;MACH,UAAU,CAAC,UAAU;AACpB,iBAAS,WAAW,KAAK;AACzB,YAAI,CAAC,SAAS,OAAO,UAAU,YAAY,EAAE,YAAY,QAAQ;AAChE,gBAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK,EAAE;QAC5D;MACD;IACD,CAAC;EACF;;;;;;;;;EAUA,MACC,OACA,SAQC;AACD,WAAO,IAAI,QAOT;MACD,MAAM,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC;MAC7C,gBAAgB,CAAC,WAAW;AAC3B,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,gBAAM,OAAO,MAAM,CAAC,EAAE,eAAe,OAAO,CAAC,CAAC;AAC9C,cAAI,QAAQ,MAAM;AACjB,mBAAO;UACR;AAEA,mBAAS;QACV;AAEA,eAAO;MACR;MACA,MAAM,CAAC,WAAW;AACjB,cAAM,SAAoB,CAAC;AAC3B,mBAAW,QAAQ,OAAO;AACzB,iBAAO,KAAK,KAAK,KAAK,MAAM,CAAC;QAC9B;AACA,eAAO;MACR;MACA,OAAO,CAAC,OAAO,WAAW;AACzB,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,gBAAM,CAAC,EAAE,MAAM,MAAM,CAAC,GAAG,MAAM;QAChC;MACD;MACA,GAAG;MACH,UAAU,CAAC,UAAU;AACpB,iBAAS,WAAW,KAAK;AACzB,YAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC1B,gBAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK,EAAE;QAC5D;AACA,YAAI,MAAM,WAAW,MAAM,QAAQ;AAClC,gBAAM,IAAI,UAAU,4BAA4B,MAAM,MAAM,WAAW,MAAM,MAAM,EAAE;QACtF;MACD;IACD,CAAC;EACF;;;;;;;;;;;;;EAcA,OACC,MACA,QACA,SAWC;AACD,UAAM,iBAAiB,OAAO,QAAQ,MAAM;AAE5C,WAAO,IAAI,QAOT;MACD;MACA,gBAAgB,CAAC,WAAW;AAC3B,YAAI,QAAQ;AACZ,mBAAW,CAAC,OAAO,IAAI,KAAK,gBAAgB;AAC3C,gBAAM,OAAO,KAAK,eAAe,OAAO,KAAK,CAAC;AAC9C,cAAI,QAAQ,MAAM;AACjB,mBAAO;UACR;AAEA,mBAAS;QACV;AAEA,eAAO;MACR;MACA,MAAM,CAAC,WAAW;AACjB,cAAM,SAAkC,CAAC;AACzC,mBAAW,CAAC,OAAO,IAAI,KAAK,gBAAgB;AAC3C,iBAAO,KAAK,IAAI,KAAK,KAAK,MAAM;QACjC;AAEA,eAAO;MACR;MACA,OAAO,CAAC,OAAO,WAAW;AACzB,mBAAW,CAAC,OAAO,IAAI,KAAK,gBAAgB;AAC3C,eAAK,MAAM,MAAM,KAAK,GAAG,MAAM;QAChC;MACD;MACA,GAAG;MACH,UAAU,CAAC,UAAU;AACpB,iBAAS,WAAW,KAAK;AACzB,YAAI,OAAO,UAAU,YAAY,SAAS,MAAM;AAC/C,gBAAM,IAAI,UAAU,0BAA0B,OAAO,KAAK,EAAE;QAC7D;MACD;IACD,CAAC;EACF;;;;;;;;;;;;;;;;;EAkBA,KACC,MACA,QACA,SAWC;AACD,UAAM,iBAAiB,OAAO,QAAQ,MAAgB;AACtD,WAAO,IAAI,QAOT;MACD;MACA,MAAM,CAAC,WAAW;AACjB,cAAM,QAAQ,OAAO,SAAS;AAE9B,cAAM,YAAY,eAAe,KAAK;AACtC,YAAI,CAAC,WAAW;AACf,gBAAM,IAAI,UAAU,iBAAiB,KAAK,aAAa,IAAI,EAAE;QAC9D;AAEA,cAAM,CAAC,MAAM,IAAI,IAAI;AAErB,eAAO;UACN,CAAC,IAAI,GAAG,MAAM,KAAK,MAAM,KAAK;UAC9B,OAAO;QACR;MACD;MACA,OAAO,CAAC,OAAO,WAAW;AACzB,cAAM,CAACE,OAAM,GAAG,IAAI,OAAO,QAAQ,KAAK,EAAE;UAAO,CAAC,CAACA,KAAI,MACtD,OAAO,OAAO,QAAQA,KAAI;QAC3B,EAAE,CAAC;AAEH,iBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC/C,gBAAM,CAAC,YAAY,UAAU,IAAI,eAAe,CAAC;AACjD,cAAI,eAAeA,OAAM;AACxB,mBAAO,UAAU,CAAC;AAClB,wBAAY,MAAM,KAAK,MAAM;AAC7B;UACD;QACD;MACD;MACA,GAAG;MACH,UAAU,CAAC,UAAU;AACpB,iBAAS,WAAW,KAAK;AACzB,YAAI,OAAO,UAAU,YAAY,SAAS,MAAM;AAC/C,gBAAM,IAAI,UAAU,0BAA0B,OAAO,KAAK,EAAE;QAC7D;AAEA,cAAM,OAAO,OAAO,KAAK,KAAK,EAAE;UAC/B,CAAC,MAAM,MAAM,CAAC,MAAM,UAAa,OAAO,OAAO,QAAQ,CAAC;QACzD;AAEA,YAAI,KAAK,WAAW,GAAG;AACtB,gBAAM,IAAI;YACT,2CAA2C,KAAK,MAAM,aAAa,IAAI;UACxE;QACD;AAEA,cAAM,CAAC,OAAO,IAAI;AAElB,YAAI,CAAC,OAAO,OAAO,QAAQ,OAAO,GAAG;AACpC,gBAAM,IAAI,UAAU,wBAAwB,OAAO,EAAE;QACtD;MACD;IACD,CAAC;EACF;;;;;;;;;EAUA,IAAkC,SAA6B,WAA+B;AAC7F,WAAO,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,SAAS,CAAC,CAAC,EAAE,UAAU;MAC5D,MAAM,OAAO,QAAQ,IAAI,KAAK,UAAU,IAAI;MAC5C,OAAO,CAAC,UAA+B;AACtC,eAAO,CAAC,GAAG,MAAM,QAAQ,CAAC;MAC3B;MACA,QAAQ,CAAC,UAAU;AAClB,cAAM,SAAS,oBAAI,IAAU;AAC7B,mBAAW,CAAC,KAAK,GAAG,KAAK,OAAO;AAC/B,iBAAO,IAAI,KAAK,GAAG;QACpB;AACA,eAAO;MACR;IACD,CAAC;EACF;;;;;EAMA,KAA6B,IAAgB;AAC5C,WAAO,YAAY,EAAE;EACtB;AACD;;;AC7hBO,IAAM,QAAQ;AAGd,IAAM,UAAU;AAGhB,IAAM,QAAQ;AAGd,IAAM,UAAU;;;ACpEvB,IAAM,oBACL;AACD,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB;AAExB,SAAS,iBAAiB,MAAuB;AACvD,MAAI,KAAK,SAAS,wBAAwB;AACzC,WAAO;EACR;AAEA,MAAI,KAAK,SAAS,GAAG,GAAG;AACvB,WAAO,kBAAkB,KAAK,IAAI;EACnC;AAEA,SAAO,oBAAoB,KAAK,IAAI;AACrC;AAEO,SAAS,mBAAmB,MAAc,SAAuB,MAAc;AACrF,QAAM,YAAY,KAAK,YAAY;AACnC,MAAI;AAEJ,MAAI,UAAU,SAAS,GAAG,GAAG;AAC5B,QAAI,CAAC,kBAAkB,KAAK,SAAS,GAAG;AACvC,YAAM,IAAI,MAAM,sBAAsB,IAAI,EAAE;IAC7C;AACA,UAAM,CAAC,QAAQ,MAAM,IAAI,UAAU,MAAM,GAAG;AAC5C,YAAQ,CAAC,GAAI,SAAS,OAAO,MAAM,GAAG,IAAI,CAAC,GAAI,MAAM;EACtD,OAAO;AACN,QAAI,CAAC,oBAAoB,KAAK,SAAS,GAAG;AACzC,YAAM,IAAI,MAAM,sBAAsB,IAAI,EAAE;IAC7C;AACA,YAAQ,UAAU,MAAM,GAAG,EAAE,MAAM,GAAG,EAAE;EACzC;AAEA,MAAI,WAAW,OAAO;AACrB,WAAO,GAAG,MAAM,KAAK,GAAG,CAAC;EAC1B;AAEA,SAAO,GAAG,MAAM,MAAM,GAAG,EAAE,EAAE,KAAK,GAAG,CAAC,IAAI,MAAM,MAAM,SAAS,CAAC,CAAC;AAClE;;;ACpCA,IAAM,eAAe;AAErB,IAAM,gBAAgB;AAEtB,IAAM,eAAe;AAErB,IAAM,iBAAiB;AAEhB,IAAM,sBAAsB,CAAC,SAA0B;AAC7D,QAAM,QAAQ,KAAK,MAAM,cAAc;AAEvC,MAAI,MAAM,SAAS,KAAK,MAAM,SAAS,EAAG,QAAO;AAEjD,QAAM,CAAC,KAAK,KAAK,OAAO,IAAI;AAG5B,MAAI,YAAY,UAAa,CAAC,cAAc,KAAK,OAAO,EAAG,QAAO;AAElE,MAAI,CAAC,iBAAiB,GAAG,EAAG,QAAO;AAGnC,SAAO,aAAa,KAAK,GAAG,KAAK,IAAI,SAAS;AAC/C;AAMO,IAAM,mBAAmB,CAAC,SAA0B;AAE1D,QAAM,YAAY,KAAK,MAAM,UAAU;AACvC,aAAW,KAAK,WAAW;AAC1B,QAAI,EAAE,SAAS,cAAc,KAAK,CAAC,oBAAoB,CAAC,EAAG,QAAO;EACnE;AAEA,SAAO;AACR;;;ACnCA,IAAM,mBAAmB;AAGlB,SAAS,yBAAyB,OAAgC;AACxE,MAAI;AACH,UAAM,SAAS,WAAW,KAAK;AAC/B,WAAO,OAAO,WAAW;EAC1B,SAAS,GAAG;AACX,WAAO;EACR;AACD;AAQO,IAAM,qBAAqB;AAC3B,SAAS,kBAAkB,OAAgC;AACjE,SAAO,MAAM,KAAK,KAAK,iBAAiB,KAAK,MAAM;AACpD;AAEO,SAAS,mBAAmB,OAAwB;AAC1D,SAAO,kBAAkB,KAAK;AAC/B;AASA,SAAS,aAAa,MAAkC;AACvD,MAAI,CAAC,KAAK,SAAS,IAAI,EAAG,QAAO;AAEjC,SAAO,eAAe,IAAI;AAC3B;AAEO,SAAS,eAAe,MAAyB;AACvD,QAAM,CAAC,SAAS,MAAM,IAAI,KAAK,MAAM,IAAI;AAEzC,QAAM,eAAe,oBAAoB,OAAO;AAEhD,QAAM,OAAO,KAAK,MAAM,QAAQ,SAAS,OAAO,SAAS,CAAC;AAC1D,QAAM,OAAO,KAAK,SAAS,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,QAAQ,GAAG,CAAC,IAAI;AACrE,QAAM,aAAa,KAAK,SAAS,GAAG,IACjC,uBAAuB,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG,KAAK,YAAY,GAAG,CAAC,CAAC,EAAE;IACjF,CAAC,cAAc,aAAa,UAAU,KAAK,CAAC;EAC7C,IACC,CAAC;AAEJ,SAAO;IACN,SAAS,eAAe,UAAU,oBAAoB,OAAO;IAC7D;IACA;IACA;EACD;AACD;AAEO,SAAS,mBAAmB,MAAkC;AACpE,QAAM,EAAE,SAAS,QAAQ,MAAM,WAAW,IACzC,OAAO,SAAS,WAAW,eAAe,IAAI,IAAI;AAEnD,QAAM,sBACL,YAAY,SAAS,IAClB,IAAI,WACH;IAAI,CAAC,cACL,OAAO,cAAc,WAAW,YAAY,mBAAmB,SAAS;EACzE,EACC,KAAK,GAAG,CAAC,MACV;AAEJ,SAAO,GAAG,OAAO,KAAK,MAAM,KAAK,IAAI,GAAG,mBAAmB;AAC5D;AAaO,SAAS,oBAAoB,OAAe,aAAsB,OAAe;AACvF,MAAI,UAAU,MAAM,YAAY;AAChC,MAAI,CAAC,cAAc,QAAQ,WAAW,IAAI,GAAG;AAC5C,cAAU,QAAQ,MAAM,CAAC;EAC1B;AACA,SAAO,KAAK,QAAQ,SAAS,qBAAqB,GAAG,GAAG,CAAC;AAC1D;AAEO,SAAS,qBAAqB,OAAe,aAAsB,OAAe;AACxF,SAAO,oBAAoB,OAAO,UAAU;AAC7C;AAEA,SAAS,MAAM,OAAwB;AACtC,SAAO,yBAAyB,KAAK,KAAK,KAAK,MAAM,SAAS,MAAM;AACrE;AAEA,SAAS,iBAAiB,OAAuB;AAChD,SAAO,WAAW,KAAK,KAAK,KAAK,MAAM,SAAS,KAAK,IAAI,MAAM,SAAS;AACzE;;;AC5GO,IAAM,eAAe;AACrB,IAAM,eAAe,OAAO,GAAU;AAEtC,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAC9B,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB,qBAAqB,KAAK;AACtD,IAAM,yBAAyB;AAC/B,IAAM,eAAe,GAAG,qBAAqB;AAC7C,IAAM,6BAAqC,qBAAqB,KAAK;;;ACPrE,IAAM,SACX,OAAO,eAAe,YAAY,YAAY,aAAa,WAAW,SAAS;;;ACO3E,SAAUC,SAAQ,GAAU;AAChC,SAAO,aAAa,cAAe,YAAY,OAAO,CAAC,KAAK,EAAE,YAAY,SAAS;AACrF;AAGM,SAAUC,SAAQ,GAAS;AAC/B,MAAI,CAAC,OAAO,cAAc,CAAC,KAAK,IAAI;AAAG,UAAM,IAAI,MAAM,oCAAoC,CAAC;AAC9F;AAGM,SAAUC,QAAO,MAA8B,SAAiB;AACpE,MAAI,CAACF,SAAQ,CAAC;AAAG,UAAM,IAAI,MAAM,qBAAqB;AACtD,MAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,MAAM;AAClD,UAAM,IAAI,MAAM,mCAAmC,UAAU,kBAAkB,EAAE,MAAM;AAC3F;AAGM,SAAU,MAAM,GAAQ;AAC5B,MAAI,OAAO,MAAM,cAAc,OAAO,EAAE,WAAW;AACjD,UAAM,IAAI,MAAM,8CAA8C;AAChE,EAAAC,SAAQ,EAAE,SAAS;AACnB,EAAAA,SAAQ,EAAE,QAAQ;AACpB;AAGM,SAAU,QAAQ,UAAe,gBAAgB,MAAI;AACzD,MAAI,SAAS;AAAW,UAAM,IAAI,MAAM,kCAAkC;AAC1E,MAAI,iBAAiB,SAAS;AAAU,UAAM,IAAI,MAAM,uCAAuC;AACjG;AAGM,SAAU,QAAQ,KAAU,UAAa;AAC7C,EAAAC,QAAO,GAAG;AACV,QAAM,MAAM,SAAS;AACrB,MAAI,IAAI,SAAS,KAAK;AACpB,UAAM,IAAI,MAAM,2DAA2D,GAAG;EAChF;AACF;AAaM,SAAU,IAAI,KAAe;AACjC,SAAO,IAAI,YAAY,IAAI,QAAQ,IAAI,YAAY,KAAK,MAAM,IAAI,aAAa,CAAC,CAAC;AACnF;AAGM,SAAU,SAAS,QAAoB;AAC3C,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAO,CAAC,EAAE,KAAK,CAAC;EAClB;AACF;AAGM,SAAU,WAAW,KAAe;AACxC,SAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AAChE;AAGM,SAAU,KAAK,MAAc,OAAa;AAC9C,SAAQ,QAAS,KAAK,QAAW,SAAS;AAC5C;AAQO,IAAM,QAAiC,MAC5C,IAAI,WAAW,IAAI,YAAY,CAAC,SAAU,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,IAAK;AAG7D,SAAU,SAAS,MAAY;AACnC,SACI,QAAQ,KAAM,aACd,QAAQ,IAAK,WACb,SAAS,IAAK,QACd,SAAS,KAAM;AAErB;AAEO,IAAM,YAAmC,OAC5C,CAAC,MAAc,IACf,CAAC,MAAc,SAAS,CAAC;AAKvB,SAAU,WAAW,KAAgB;AACzC,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,QAAI,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC;EAC1B;AACA,SAAO;AACT;AAEO,IAAM,aAA8C,OACvD,CAAC,MAAmB,IACpB;AAGJ,IAAMC,kBAA0C;;EAE9C,OAAO,WAAW,KAAK,CAAA,CAAE,EAAE,UAAU,cAAc,OAAO,WAAW,YAAY;GAAW;AAG9F,IAAM,QAAwB,MAAM,KAAK,EAAE,QAAQ,IAAG,GAAI,CAAC,GAAG,MAC5D,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AAO3B,SAAU,WAAW,OAAiB;AAC1C,EAAAC,QAAO,KAAK;AAEZ,MAAID;AAAe,WAAO,MAAM,MAAK;AAErC,MAAIE,OAAM;AACV,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,IAAAA,QAAO,MAAM,MAAM,CAAC,CAAC;EACvB;AACA,SAAOA;AACT;AAGA,IAAM,SAAS,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAG;AAC5D,SAAS,cAAc,IAAU;AAC/B,MAAI,MAAM,OAAO,MAAM,MAAM,OAAO;AAAI,WAAO,KAAK,OAAO;AAC3D,MAAI,MAAM,OAAO,KAAK,MAAM,OAAO;AAAG,WAAO,MAAM,OAAO,IAAI;AAC9D,MAAI,MAAM,OAAO,KAAK,MAAM,OAAO;AAAG,WAAO,MAAM,OAAO,IAAI;AAC9D;AACF;AAMM,SAAU,WAAWA,MAAW;AACpC,MAAI,OAAOA,SAAQ;AAAU,UAAM,IAAI,MAAM,8BAA8B,OAAOA,IAAG;AAErF,MAAIF;AAAe,WAAO,WAAW,QAAQE,IAAG;AAChD,QAAM,KAAKA,KAAI;AACf,QAAM,KAAK,KAAK;AAChB,MAAI,KAAK;AAAG,UAAM,IAAI,MAAM,qDAAqD,EAAE;AACnF,QAAM,QAAQ,IAAI,WAAW,EAAE;AAC/B,WAAS,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,MAAM,MAAM,GAAG;AAC/C,UAAM,KAAK,cAAcA,KAAI,WAAW,EAAE,CAAC;AAC3C,UAAM,KAAK,cAAcA,KAAI,WAAW,KAAK,CAAC,CAAC;AAC/C,QAAI,OAAO,UAAa,OAAO,QAAW;AACxC,YAAM,OAAOA,KAAI,EAAE,IAAIA,KAAI,KAAK,CAAC;AACjC,YAAM,IAAI,MAAM,iDAAiD,OAAO,gBAAgB,EAAE;IAC5F;AACA,UAAM,EAAE,IAAI,KAAK,KAAK;EACxB;AACA,SAAO;AACT;AAkCM,SAAU,YAAY,KAAW;AACrC,MAAI,OAAO,QAAQ;AAAU,UAAM,IAAI,MAAM,iBAAiB;AAC9D,SAAO,IAAI,WAAW,IAAI,YAAW,EAAG,OAAO,GAAG,CAAC;AACrD;AAiBM,SAAU,QAAQ,MAAW;AACjC,MAAI,OAAO,SAAS;AAAU,WAAO,YAAY,IAAI;AACrD,EAAAC,QAAO,IAAI;AACX,SAAO;AACT;AAQM,SAAU,gBAAgB,MAAc;AAC5C,MAAI,OAAO,SAAS;AAAU,WAAO,YAAY,IAAI;AACrD,EAAAA,QAAO,IAAI;AACX,SAAO;AACT;AAGM,SAAU,eAAe,QAAoB;AACjD,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,IAAI,OAAO,CAAC;AAClB,IAAAA,QAAO,CAAC;AACR,WAAO,EAAE;EACX;AACA,QAAM,MAAM,IAAI,WAAW,GAAG;AAC9B,WAAS,IAAI,GAAG,MAAM,GAAG,IAAI,OAAO,QAAQ,KAAK;AAC/C,UAAM,IAAI,OAAO,CAAC;AAClB,QAAI,IAAI,GAAG,GAAG;AACd,WAAO,EAAE;EACX;AACA,SAAO;AACT;AAGM,SAAU,UACd,UACA,MAAS;AAET,MAAI,SAAS,UAAa,CAAA,EAAG,SAAS,KAAK,IAAI,MAAM;AACnD,UAAM,IAAI,MAAM,uCAAuC;AACzD,QAAM,SAAS,OAAO,OAAO,UAAU,IAAI;AAC3C,SAAO;AACT;AAWM,IAAgB,OAAhB,MAAoB;;AA4CpB,SAAU,aACd,UAAuB;AAOvB,QAAM,QAAQ,CAAC,QAA2B,SAAQ,EAAG,OAAO,QAAQ,GAAG,CAAC,EAAE,OAAM;AAChF,QAAM,MAAM,SAAQ;AACpB,QAAM,YAAY,IAAI;AACtB,QAAM,WAAW,IAAI;AACrB,QAAM,SAAS,MAAM,SAAQ;AAC7B,SAAO;AACT;AAEM,SAAU,gBACd,UAA+B;AAO/B,QAAM,QAAQ,CAAC,KAAY,SAAyB,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,CAAC,EAAE,OAAM;AAC9F,QAAM,MAAM,SAAS,CAAA,CAAO;AAC5B,QAAM,YAAY,IAAI;AACtB,QAAM,WAAW,IAAI;AACrB,QAAM,SAAS,CAAC,SAAa,SAAS,IAAI;AAC1C,SAAO;AACT;AAEM,SAAU,YACd,UAAkC;AAOlC,QAAM,QAAQ,CAAC,KAAY,SAAyB,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,CAAC,EAAE,OAAM;AAC9F,QAAM,MAAM,SAAS,CAAA,CAAO;AAC5B,QAAM,YAAY,IAAI;AACtB,QAAM,WAAW,IAAI;AACrB,QAAM,SAAS,CAAC,SAAa,SAAS,IAAI;AAC1C,SAAO;AACT;AAMM,SAAU,YAAY,cAAc,IAAE;AAC1C,MAAI,UAAU,OAAO,OAAO,oBAAoB,YAAY;AAC1D,WAAO,OAAO,gBAAgB,IAAI,WAAW,WAAW,CAAC;EAC3D;AAEA,MAAI,UAAU,OAAO,OAAO,gBAAgB,YAAY;AACtD,WAAO,WAAW,KAAK,OAAO,YAAY,WAAW,CAAC;EACxD;AACA,QAAM,IAAI,MAAM,wCAAwC;AAC1D;;;AC/XO,IAAM,SAAqC,WAAW,KAAK;EAChE;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAI;EAAI;EAAI;EAAI;EAAI;EAClD;EAAI;EAAI;EAAG;EAAG;EAAG;EAAI;EAAI;EAAG;EAAG;EAAI;EAAG;EAAG;EAAI;EAAG;EAAG;EACnD;EAAI;EAAG;EAAI;EAAG;EAAG;EAAG;EAAI;EAAI;EAAI;EAAI;EAAG;EAAG;EAAG;EAAG;EAAG;EACnD;EAAG;EAAG;EAAG;EAAG;EAAI;EAAI;EAAI;EAAI;EAAG;EAAG;EAAG;EAAI;EAAG;EAAG;EAAI;EACnD;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAI;EAAI;EAAI;EAAG;EAAI;EAAI;EAAG;EAAG;EAAG;EAClD;EAAG;EAAI;EAAG;EAAI;EAAG;EAAI;EAAG;EAAG;EAAG;EAAI;EAAG;EAAG;EAAI;EAAI;EAAG;EACnD;EAAI;EAAG;EAAG;EAAI;EAAI;EAAI;EAAG;EAAI;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAClD;EAAI;EAAI;EAAG;EAAI;EAAI;EAAG;EAAG;EAAG;EAAG;EAAG;EAAI;EAAG;EAAG;EAAG;EAAG;EAClD;EAAG;EAAI;EAAI;EAAG;EAAI;EAAG;EAAG;EAAG;EAAI;EAAG;EAAI;EAAG;EAAG;EAAG;EAAI;EACnD;EAAI;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAI;EAAI;EAAG;EAAI;EAAG;EAAI;EAAI;EACnD;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAI;EAAI;EAAI;EAAI;EAAI;EAClD;EAAI;EAAI;EAAG;EAAG;EAAG;EAAI;EAAI;EAAG;EAAG;EAAI;EAAG;EAAG;EAAI;EAAG;EAAG;;EAEnD;EAAI;EAAG;EAAI;EAAG;EAAG;EAAG;EAAI;EAAI;EAAI;EAAI;EAAG;EAAG;EAAG;EAAG;EAAG;EACnD;EAAG;EAAG;EAAG;EAAG;EAAI;EAAI;EAAI;EAAI;EAAG;EAAG;EAAG;EAAI;EAAG;EAAG;EAAI;EACnD;EAAG;EAAG;EAAG;EAAG;EAAG;EAAG;EAAI;EAAI;EAAI;EAAG;EAAI;EAAI;EAAG;EAAG;EAAG;EAClD;EAAG;EAAI;EAAG;EAAI;EAAG;EAAI;EAAG;EAAG;EAAG;EAAI;EAAG;EAAG;EAAI;EAAI;EAAG;CACpD;AAMK,SAAU,IAAI,GAAW,GAAW,GAAW,GAAW,GAAS;AACvE,MAAK,IAAI,IAAI,IAAK;AAClB,MAAI,KAAK,IAAI,GAAG,EAAE;AAClB,MAAK,IAAI,IAAK;AACd,MAAI,KAAK,IAAI,GAAG,EAAE;AAClB,SAAO,EAAE,GAAG,GAAG,GAAG,EAAC;AACrB;AAEM,SAAU,IAAI,GAAW,GAAW,GAAW,GAAW,GAAS;AACvE,MAAK,IAAI,IAAI,IAAK;AAClB,MAAI,KAAK,IAAI,GAAG,CAAC;AACjB,MAAK,IAAI,IAAK;AACd,MAAI,KAAK,IAAI,GAAG,CAAC;AACjB,SAAO,EAAE,GAAG,GAAG,GAAG,EAAC;AACrB;;;AC1CM,SAAU,aACd,MACA,YACA,OACAC,OAAa;AAEb,MAAI,OAAO,KAAK,iBAAiB;AAAY,WAAO,KAAK,aAAa,YAAY,OAAOA,KAAI;AAC7F,QAAMC,QAAO,OAAO,EAAE;AACtB,QAAM,WAAW,OAAO,UAAU;AAClC,QAAM,KAAK,OAAQ,SAASA,QAAQ,QAAQ;AAC5C,QAAM,KAAK,OAAO,QAAQ,QAAQ;AAClC,QAAM,IAAID,QAAO,IAAI;AACrB,QAAM,IAAIA,QAAO,IAAI;AACrB,OAAK,UAAU,aAAa,GAAG,IAAIA,KAAI;AACvC,OAAK,UAAU,aAAa,GAAG,IAAIA,KAAI;AACzC;AAGM,SAAU,IAAI,GAAW,GAAW,GAAS;AACjD,SAAQ,IAAI,IAAM,CAAC,IAAI;AACzB;AAGM,SAAU,IAAI,GAAW,GAAW,GAAS;AACjD,SAAQ,IAAI,IAAM,IAAI,IAAM,IAAI;AAClC;AAMM,IAAgB,SAAhB,cAAoD,KAAO;EAoB/D,YAAY,UAAkB,WAAmB,WAAmBA,OAAa;AAC/E,UAAK;AANG,SAAA,WAAW;AACX,SAAA,SAAS;AACT,SAAA,MAAM;AACN,SAAA,YAAY;AAIpB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,OAAOA;AACZ,SAAK,SAAS,IAAI,WAAW,QAAQ;AACrC,SAAK,OAAO,WAAW,KAAK,MAAM;EACpC;EACA,OAAO,MAAW;AAChB,YAAQ,IAAI;AACZ,WAAO,QAAQ,IAAI;AACnB,IAAAE,QAAO,IAAI;AACX,UAAM,EAAE,MAAM,QAAQ,SAAQ,IAAK;AACnC,UAAM,MAAM,KAAK;AACjB,aAAS,MAAM,GAAG,MAAM,OAAO;AAC7B,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,GAAG;AAEpD,UAAI,SAAS,UAAU;AACrB,cAAM,WAAW,WAAW,IAAI;AAChC,eAAO,YAAY,MAAM,KAAK,OAAO;AAAU,eAAK,QAAQ,UAAU,GAAG;AACzE;MACF;AACA,aAAO,IAAI,KAAK,SAAS,KAAK,MAAM,IAAI,GAAG,KAAK,GAAG;AACnD,WAAK,OAAO;AACZ,aAAO;AACP,UAAI,KAAK,QAAQ,UAAU;AACzB,aAAK,QAAQ,MAAM,CAAC;AACpB,aAAK,MAAM;MACb;IACF;AACA,SAAK,UAAU,KAAK;AACpB,SAAK,WAAU;AACf,WAAO;EACT;EACA,WAAW,KAAe;AACxB,YAAQ,IAAI;AACZ,YAAQ,KAAK,IAAI;AACjB,SAAK,WAAW;AAIhB,UAAM,EAAE,QAAQ,MAAM,UAAU,MAAAF,MAAI,IAAK;AACzC,QAAI,EAAE,IAAG,IAAK;AAEd,WAAO,KAAK,IAAI;AAChB,UAAM,KAAK,OAAO,SAAS,GAAG,CAAC;AAG/B,QAAI,KAAK,YAAY,WAAW,KAAK;AACnC,WAAK,QAAQ,MAAM,CAAC;AACpB,YAAM;IACR;AAEA,aAAS,IAAI,KAAK,IAAI,UAAU;AAAK,aAAO,CAAC,IAAI;AAIjD,iBAAa,MAAM,WAAW,GAAG,OAAO,KAAK,SAAS,CAAC,GAAGA,KAAI;AAC9D,SAAK,QAAQ,MAAM,CAAC;AACpB,UAAM,QAAQ,WAAW,GAAG;AAC5B,UAAM,MAAM,KAAK;AAEjB,QAAI,MAAM;AAAG,YAAM,IAAI,MAAM,6CAA6C;AAC1E,UAAM,SAAS,MAAM;AACrB,UAAM,QAAQ,KAAK,IAAG;AACtB,QAAI,SAAS,MAAM;AAAQ,YAAM,IAAI,MAAM,oCAAoC;AAC/E,aAAS,IAAI,GAAG,IAAI,QAAQ;AAAK,YAAM,UAAU,IAAI,GAAG,MAAM,CAAC,GAAGA,KAAI;EACxE;EACA,SAAM;AACJ,UAAM,EAAE,QAAQ,UAAS,IAAK;AAC9B,SAAK,WAAW,MAAM;AACtB,UAAM,MAAM,OAAO,MAAM,GAAG,SAAS;AACrC,SAAK,QAAO;AACZ,WAAO;EACT;EACA,WAAW,IAAM;AACf,WAAA,KAAO,IAAK,KAAK,YAAmB;AACpC,OAAG,IAAI,GAAG,KAAK,IAAG,CAAE;AACpB,UAAM,EAAE,UAAU,QAAQ,QAAQ,UAAU,WAAW,IAAG,IAAK;AAC/D,OAAG,YAAY;AACf,OAAG,WAAW;AACd,OAAG,SAAS;AACZ,OAAG,MAAM;AACT,QAAI,SAAS;AAAU,SAAG,OAAO,IAAI,MAAM;AAC3C,WAAO;EACT;EACA,QAAK;AACH,WAAO,KAAK,WAAU;EACxB;;AASK,IAAM,YAAyC,YAAY,KAAK;EACrE;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAGM,IAAM,YAAyC,YAAY,KAAK;EACrE;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAGM,IAAM,YAAyC,YAAY,KAAK;EACrE;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAGM,IAAM,YAAyC,YAAY,KAAK;EACrE;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;;;AC1KD,IAAM,aAA6B,OAAO,KAAK,KAAK,CAAC;AACrD,IAAM,OAAuB,OAAO,EAAE;AAEtC,SAAS,QACP,GACA,KAAK,OAAK;AAKV,MAAI;AAAI,WAAO,EAAE,GAAG,OAAO,IAAI,UAAU,GAAG,GAAG,OAAQ,KAAK,OAAQ,UAAU,EAAC;AAC/E,SAAO,EAAE,GAAG,OAAQ,KAAK,OAAQ,UAAU,IAAI,GAAG,GAAG,OAAO,IAAI,UAAU,IAAI,EAAC;AACjF;AAEA,SAAS,MAAM,KAAe,KAAK,OAAK;AACtC,QAAM,MAAM,IAAI;AAChB,MAAI,KAAK,IAAI,YAAY,GAAG;AAC5B,MAAI,KAAK,IAAI,YAAY,GAAG;AAC5B,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,EAAE,GAAG,EAAC,IAAK,QAAQ,IAAI,CAAC,GAAG,EAAE;AACnC,KAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;EACxB;AACA,SAAO,CAAC,IAAI,EAAE;AAChB;AAIA,IAAM,QAAQ,CAAC,GAAW,IAAY,MAAsB,MAAM;AAClE,IAAM,QAAQ,CAAC,GAAW,GAAW,MAAuB,KAAM,KAAK,IAAO,MAAM;AAEpF,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,MAAM,IAAM,KAAM,KAAK;AACpF,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,KAAK,IAAO,MAAM;AAErF,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,KAAK,IAAO,MAAO,IAAI;AAC1F,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,MAAO,IAAI,KAAQ,KAAM,KAAK;AAE3F,IAAM,UAAU,CAAC,IAAY,MAAsB;AACnD,IAAM,UAAU,CAAC,GAAW,OAAuB;AAEnD,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAK,IAAM,MAAO,KAAK;AACpF,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAK,IAAM,MAAO,KAAK;AAEpF,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,IAAI,KAAQ,MAAO,KAAK;AAC3F,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,IAAI,KAAQ,MAAO,KAAK;AAI3F,SAAS,IACP,IACA,IACA,IACA,IAAU;AAKV,QAAM,KAAK,OAAO,MAAM,OAAO;AAC/B,SAAO,EAAE,GAAI,KAAK,MAAO,IAAI,KAAK,KAAM,KAAM,GAAG,GAAG,IAAI,EAAC;AAC3D;AAEA,IAAM,QAAQ,CAAC,IAAY,IAAY,QAAwB,OAAO,MAAM,OAAO,MAAM,OAAO;AAChG,IAAM,QAAQ,CAAC,KAAa,IAAY,IAAY,OACjD,KAAK,KAAK,MAAO,MAAM,KAAK,KAAM,KAAM;AAC3C,IAAM,QAAQ,CAAC,IAAY,IAAY,IAAY,QAChD,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO;AACjD,IAAM,QAAQ,CAAC,KAAa,IAAY,IAAY,IAAY,OAC7D,KAAK,KAAK,KAAK,MAAO,MAAM,KAAK,KAAM,KAAM;AAChD,IAAM,QAAQ,CAAC,IAAY,IAAY,IAAY,IAAY,QAC5D,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO;AAC9D,IAAM,QAAQ,CAAC,KAAa,IAAY,IAAY,IAAY,IAAY,OACzE,KAAK,KAAK,KAAK,KAAK,MAAO,MAAM,KAAK,KAAM,KAAM;;;ACnDrD,IAAM,SAAyB,YAAY,KAAK;EAC9C;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAED,IAAM,OAAuB,IAAI,YAAY,EAAE;AAG/C,SAAS,IAAI,GAAW,GAAW,GAAW,GAAW,KAAkB,GAAS;AAElF,QAAM,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC;AACjC,MAAI,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,IAAI,CAAC;AACzC,MAAI,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,IAAI,CAAC;AACzC,MAAI,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,IAAI,CAAC;AACzC,MAAI,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,IAAI,CAAC;AAEzC,MAAI,KAAS,MAAM,IAAI,IAAI,EAAE;AAC7B,OAAS,MAAM,IAAI,IAAI,IAAI,EAAE;AAC7B,OAAK,KAAK;AAEV,GAAC,EAAE,IAAI,GAAE,IAAK,EAAE,IAAI,KAAK,IAAI,IAAI,KAAK,GAAE;AACxC,GAAC,EAAE,IAAI,GAAE,IAAK,EAAE,IAAQ,QAAQ,IAAI,EAAE,GAAG,IAAQ,QAAQ,IAAI,EAAE,EAAC;AAEhE,GAAC,EAAE,GAAG,IAAI,GAAG,GAAE,IAAS,IAAI,IAAI,IAAI,IAAI,EAAE;AAE1C,GAAC,EAAE,IAAI,GAAE,IAAK,EAAE,IAAI,KAAK,IAAI,IAAI,KAAK,GAAE;AACxC,GAAC,EAAE,IAAI,GAAE,IAAK,EAAE,IAAQ,OAAO,IAAI,IAAI,EAAE,GAAG,IAAQ,OAAO,IAAI,IAAI,EAAE,EAAC;AACtE,EAAC,KAAK,IAAI,CAAC,IAAI,IAAM,KAAK,IAAI,IAAI,CAAC,IAAI;AACvC,EAAC,KAAK,IAAI,CAAC,IAAI,IAAM,KAAK,IAAI,IAAI,CAAC,IAAI;AACvC,EAAC,KAAK,IAAI,CAAC,IAAI,IAAM,KAAK,IAAI,IAAI,CAAC,IAAI;AACvC,EAAC,KAAK,IAAI,CAAC,IAAI,IAAM,KAAK,IAAI,IAAI,CAAC,IAAI;AACzC;AAEA,SAAS,IAAI,GAAW,GAAW,GAAW,GAAW,KAAkB,GAAS;AAElF,QAAM,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC;AACjC,MAAI,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,IAAI,CAAC;AACzC,MAAI,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,IAAI,CAAC;AACzC,MAAI,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,IAAI,CAAC;AACzC,MAAI,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,IAAI,CAAC;AAEzC,MAAI,KAAS,MAAM,IAAI,IAAI,EAAE;AAC7B,OAAS,MAAM,IAAI,IAAI,IAAI,EAAE;AAC7B,OAAK,KAAK;AAEV,GAAC,EAAE,IAAI,GAAE,IAAK,EAAE,IAAI,KAAK,IAAI,IAAI,KAAK,GAAE;AACxC,GAAC,EAAE,IAAI,GAAE,IAAK,EAAE,IAAQ,OAAO,IAAI,IAAI,EAAE,GAAG,IAAQ,OAAO,IAAI,IAAI,EAAE,EAAC;AAEtE,GAAC,EAAE,GAAG,IAAI,GAAG,GAAE,IAAS,IAAI,IAAI,IAAI,IAAI,EAAE;AAE1C,GAAC,EAAE,IAAI,GAAE,IAAK,EAAE,IAAI,KAAK,IAAI,IAAI,KAAK,GAAE;AACxC,GAAC,EAAE,IAAI,GAAE,IAAK,EAAE,IAAQ,OAAO,IAAI,IAAI,EAAE,GAAG,IAAQ,OAAO,IAAI,IAAI,EAAE,EAAC;AACtE,EAAC,KAAK,IAAI,CAAC,IAAI,IAAM,KAAK,IAAI,IAAI,CAAC,IAAI;AACvC,EAAC,KAAK,IAAI,CAAC,IAAI,IAAM,KAAK,IAAI,IAAI,CAAC,IAAI;AACvC,EAAC,KAAK,IAAI,CAAC,IAAI,IAAM,KAAK,IAAI,IAAI,CAAC,IAAI;AACvC,EAAC,KAAK,IAAI,CAAC,IAAI,IAAM,KAAK,IAAI,IAAI,CAAC,IAAI;AACzC;AAEA,SAAS,gBACP,WACA,OAA+B,CAAA,GAC/B,QACA,SACA,SAAe;AAEf,EAAAG,SAAQ,MAAM;AACd,MAAI,YAAY,KAAK,YAAY;AAAQ,UAAM,IAAI,MAAM,8BAA8B;AACvF,QAAM,EAAE,KAAK,MAAM,gBAAe,IAAK;AACvC,MAAI,QAAQ,WAAc,IAAI,SAAS,KAAK,IAAI,SAAS;AACvD,UAAM,IAAI,MAAM,wCAAwC,MAAM;AAChE,MAAI,SAAS,UAAa,KAAK,WAAW;AACxC,UAAM,IAAI,MAAM,+BAA+B,OAAO;AACxD,MAAI,oBAAoB,UAAa,gBAAgB,WAAW;AAC9D,UAAM,IAAI,MAAM,0CAA0C,OAAO;AACrE;AAGM,IAAgB,SAAhB,cAAoD,KAAO;EAc/D,YAAY,UAAkB,WAAiB;AAC7C,UAAK;AARG,SAAA,WAAW;AACX,SAAA,YAAY;AACZ,SAAA,SAAiB;AACjB,SAAA,MAAc;AAMtB,IAAAA,SAAQ,QAAQ;AAChB,IAAAA,SAAQ,SAAS;AACjB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,SAAS,IAAI,WAAW,QAAQ;AACrC,SAAK,WAAW,IAAI,KAAK,MAAM;EACjC;EACA,OAAO,MAAW;AAChB,YAAQ,IAAI;AACZ,WAAO,QAAQ,IAAI;AACnB,IAAAC,QAAO,IAAI;AAKX,UAAM,EAAE,UAAU,QAAQ,SAAQ,IAAK;AACvC,UAAM,MAAM,KAAK;AACjB,UAAM,SAAS,KAAK;AACpB,UAAM,MAAM,KAAK;AACjB,aAAS,MAAM,GAAG,MAAM,OAAO;AAE7B,UAAI,KAAK,QAAQ,UAAU;AACzB,mBAAW,QAAQ;AACnB,aAAK,SAAS,UAAU,GAAG,KAAK;AAChC,mBAAW,QAAQ;AACnB,aAAK,MAAM;MACb;AACA,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,GAAG;AACpD,YAAM,aAAa,SAAS;AAE5B,UAAI,SAAS,YAAY,EAAE,aAAa,MAAM,MAAM,OAAO,KAAK;AAC9D,cAAM,SAAS,IAAI,YAAY,KAAK,YAAY,KAAK,OAAO,MAAM,OAAO,CAAC,CAAC;AAC3E,mBAAW,MAAM;AACjB,iBAAS,QAAQ,GAAG,MAAM,WAAW,KAAK,SAAS,SAAS,QAAQ,OAAO,UAAU;AACnF,eAAK,UAAU;AACf,eAAK,SAAS,QAAQ,OAAO,KAAK;QACpC;AACA,mBAAW,MAAM;AACjB;MACF;AACA,aAAO,IAAI,KAAK,SAAS,KAAK,MAAM,IAAI,GAAG,KAAK,GAAG;AACnD,WAAK,OAAO;AACZ,WAAK,UAAU;AACf,aAAO;IACT;AACA,WAAO;EACT;EACA,WAAW,KAAe;AACxB,YAAQ,IAAI;AACZ,YAAQ,KAAK,IAAI;AACjB,UAAM,EAAE,KAAK,SAAQ,IAAK;AAC1B,SAAK,WAAW;AAEhB,UAAM,KAAK,OAAO,SAAS,GAAG,CAAC;AAC/B,eAAW,QAAQ;AACnB,SAAK,SAAS,UAAU,GAAG,IAAI;AAC/B,eAAW,QAAQ;AACnB,UAAM,QAAQ,IAAI,GAAG;AACrB,SAAK,IAAG,EAAG,QAAQ,CAAC,GAAG,MAAO,MAAM,CAAC,IAAI,UAAU,CAAC,CAAE;EACxD;EACA,SAAM;AACJ,UAAM,EAAE,QAAQ,UAAS,IAAK;AAC9B,SAAK,WAAW,MAAM;AACtB,UAAM,MAAM,OAAO,MAAM,GAAG,SAAS;AACrC,SAAK,QAAO;AACZ,WAAO;EACT;EACA,WAAW,IAAM;AACf,UAAM,EAAE,QAAQ,QAAQ,UAAU,WAAW,WAAW,IAAG,IAAK;AAChE,WAAA,KAAO,IAAK,KAAK,YAAoB,EAAE,OAAO,UAAS,CAAE;AACzD,OAAG,IAAI,GAAG,KAAK,IAAG,CAAE;AACpB,OAAG,OAAO,IAAI,MAAM;AACpB,OAAG,YAAY;AACf,OAAG,WAAW;AACd,OAAG,SAAS;AACZ,OAAG,MAAM;AAET,OAAG,YAAY;AACf,WAAO;EACT;EACA,QAAK;AACH,WAAO,KAAK,WAAU;EACxB;;AAGI,IAAO,UAAP,cAAuB,OAAe;EAmB1C,YAAY,OAAmB,CAAA,GAAE;AAC/B,UAAM,OAAO,KAAK,UAAU,SAAY,KAAK,KAAK;AAClD,UAAM,KAAK,IAAI;AAnBT,SAAA,MAAM,OAAO,CAAC,IAAI;AAClB,SAAA,MAAM,OAAO,CAAC,IAAI;AAClB,SAAA,MAAM,OAAO,CAAC,IAAI;AAClB,SAAA,MAAM,OAAO,CAAC,IAAI;AAClB,SAAA,MAAM,OAAO,CAAC,IAAI;AAClB,SAAA,MAAM,OAAO,CAAC,IAAI;AAClB,SAAA,MAAM,OAAO,CAAC,IAAI;AAClB,SAAA,MAAM,OAAO,CAAC,IAAI;AAClB,SAAA,MAAM,OAAO,CAAC,IAAI;AAClB,SAAA,MAAM,OAAO,CAAC,IAAI;AAClB,SAAA,MAAM,OAAO,EAAE,IAAI;AACnB,SAAA,MAAM,OAAO,EAAE,IAAI;AACnB,SAAA,MAAM,OAAO,EAAE,IAAI;AACnB,SAAA,MAAM,OAAO,EAAE,IAAI;AACnB,SAAA,MAAM,OAAO,EAAE,IAAI;AACnB,SAAA,MAAM,OAAO,EAAE,IAAI;AAKzB,oBAAgB,MAAM,MAAM,IAAI,IAAI,EAAE;AACtC,QAAI,EAAE,KAAK,iBAAiB,KAAI,IAAK;AACrC,QAAI,YAAY;AAChB,QAAI,QAAQ,QAAW;AACrB,YAAM,QAAQ,GAAG;AACjB,kBAAY,IAAI;IAClB;AACA,SAAK,OAAO,KAAK,YAAa,aAAa,IAAM,KAAQ,KAAO,KAAQ;AACxE,QAAI,SAAS,QAAW;AACtB,aAAO,QAAQ,IAAI;AACnB,YAAM,MAAM,IAAI,IAAI;AACpB,WAAK,OAAO,UAAU,IAAI,CAAC,CAAC;AAC5B,WAAK,OAAO,UAAU,IAAI,CAAC,CAAC;AAC5B,WAAK,OAAO,UAAU,IAAI,CAAC,CAAC;AAC5B,WAAK,OAAO,UAAU,IAAI,CAAC,CAAC;IAC9B;AACA,QAAI,oBAAoB,QAAW;AACjC,wBAAkB,QAAQ,eAAe;AACzC,YAAM,OAAO,IAAI,eAAe;AAChC,WAAK,OAAO,UAAU,KAAK,CAAC,CAAC;AAC7B,WAAK,OAAO,UAAU,KAAK,CAAC,CAAC;AAC7B,WAAK,OAAO,UAAU,KAAK,CAAC,CAAC;AAC7B,WAAK,OAAO,UAAU,KAAK,CAAC,CAAC;IAC/B;AACA,QAAI,QAAQ,QAAW;AAErB,YAAM,MAAM,IAAI,WAAW,KAAK,QAAQ;AACxC,UAAI,IAAI,GAAG;AACX,WAAK,OAAO,GAAG;IACjB;EACF;;EAEU,MAAG;AAIX,QAAI,EAAE,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAG,IAAK;AACzF,WAAO,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;EACxF;;EAEU,IACR,KAAa,KAAa,KAAa,KACvC,KAAa,KAAa,KAAa,KACvC,KAAa,KAAa,KAAa,KACvC,KAAa,KAAa,KAAa,KAAW;AAElD,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,MAAM;EACnB;EACU,SAAS,KAAkB,QAAgB,QAAe;AAClE,SAAK,IAAG,EAAG,QAAQ,CAAC,GAAG,MAAO,KAAK,CAAC,IAAI,CAAE;AAC1C,SAAK,IAAI,QAAQ,EAAE;AACnB,QAAI,EAAE,GAAG,EAAC,IAAS,QAAQ,OAAO,KAAK,MAAM,CAAC;AAC9C,SAAK,EAAE,IAAI,OAAO,CAAC,IAAI;AACvB,SAAK,EAAE,IAAI,OAAO,CAAC,IAAI;AAEvB,QAAI,QAAQ;AACV,WAAK,EAAE,IAAI,CAAC,KAAK,EAAE;AACnB,WAAK,EAAE,IAAI,CAAC,KAAK,EAAE;IACrB;AACA,QAAI,IAAI;AACR,UAAM,IAAI;AACV,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,UAAI,GAAG,GAAG,GAAG,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AACzC,UAAI,GAAG,GAAG,GAAG,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AACzC,UAAI,GAAG,GAAG,GAAG,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AACzC,UAAI,GAAG,GAAG,GAAG,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AACzC,UAAI,GAAG,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AAC1C,UAAI,GAAG,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AAC1C,UAAI,GAAG,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AAC1C,UAAI,GAAG,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AAE1C,UAAI,GAAG,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AAC1C,UAAI,GAAG,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AAC1C,UAAI,GAAG,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AAC1C,UAAI,GAAG,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AAC1C,UAAI,GAAG,GAAG,GAAG,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AACzC,UAAI,GAAG,GAAG,GAAG,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AACzC,UAAI,GAAG,GAAG,GAAG,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;AACzC,UAAI,GAAG,GAAG,GAAG,IAAI,KAAK,SAAS,IAAI,EAAE,GAAG,CAAC;IAC3C;AACA,SAAK,OAAO,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,SAAK,OAAO,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,SAAK,OAAO,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,SAAK,OAAO,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,SAAK,OAAO,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,SAAK,OAAO,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,SAAK,OAAO,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,SAAK,OAAO,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,SAAK,OAAO,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,SAAK,OAAO,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,SAAK,OAAO,KAAK,EAAE,IAAI,KAAK,EAAE;AAC9B,SAAK,OAAO,KAAK,EAAE,IAAI,KAAK,EAAE;AAC9B,SAAK,OAAO,KAAK,EAAE,IAAI,KAAK,EAAE;AAC9B,SAAK,OAAO,KAAK,EAAE,IAAI,KAAK,EAAE;AAC9B,SAAK,OAAO,KAAK,EAAE,IAAI,KAAK,EAAE;AAC9B,SAAK,OAAO,KAAK,EAAE,IAAI,KAAK,EAAE;AAC9B,UAAM,IAAI;EACZ;EACA,UAAO;AACL,SAAK,YAAY;AACjB,UAAM,KAAK,QAAQ;AACnB,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACzD;;AAQK,IAAM,UAAkC,gBAC7C,CAAC,SAAS,IAAI,QAAQ,IAAI,CAAC;AAgBvB,SAAU,SAAS,GAAe,QAAgB,KAAkB,QACxE,IAAY,IAAY,IAAY,IAAY,IAAY,IAAY,IAAY,IACpF,IAAY,IAAY,KAAa,KAAa,KAAa,KAAa,KAAa,KAAW;AAEpG,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC5E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC5E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC5E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC5E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC9E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC9E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC9E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAE9E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC9E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC9E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC9E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC9E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC5E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC5E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;AAC5E,KAAC,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAG,IAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,SAAS,EAAE,GAAG,CAAC,CAAC;EAC9E;AACA,SAAO,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAG;AAC/E;AAEA,IAAM,SAAS;AACT,IAAO,UAAP,cAAuB,OAAe;EAW1C,YAAY,OAAmB,CAAA,GAAE;AAC/B,UAAM,OAAO,KAAK,UAAU,SAAY,KAAK,KAAK;AAClD,UAAM,IAAI,IAAI;AAXR,SAAA,KAAK,OAAO,CAAC,IAAI;AACjB,SAAA,KAAK,OAAO,CAAC,IAAI;AACjB,SAAA,KAAK,OAAO,CAAC,IAAI;AACjB,SAAA,KAAK,OAAO,CAAC,IAAI;AACjB,SAAA,KAAK,OAAO,CAAC,IAAI;AACjB,SAAA,KAAK,OAAO,CAAC,IAAI;AACjB,SAAA,KAAK,OAAO,CAAC,IAAI;AACjB,SAAA,KAAK,OAAO,CAAC,IAAI;AAKvB,oBAAgB,MAAM,MAAM,IAAI,GAAG,CAAC;AACpC,QAAI,EAAE,KAAK,iBAAiB,KAAI,IAAK;AACrC,QAAI,YAAY;AAChB,QAAI,QAAQ,QAAW;AACrB,YAAM,QAAQ,GAAG;AACjB,kBAAY,IAAI;IAClB;AACA,SAAK,MAAM,KAAK,YAAa,aAAa,IAAM,KAAQ,KAAO,KAAQ;AACvE,QAAI,SAAS,QAAW;AACtB,aAAO,QAAQ,IAAI;AACnB,YAAM,MAAM,IAAI,IAAkB;AAClC,WAAK,MAAM,UAAU,IAAI,CAAC,CAAC;AAC3B,WAAK,MAAM,UAAU,IAAI,CAAC,CAAC;IAC7B;AACA,QAAI,oBAAoB,QAAW;AACjC,wBAAkB,QAAQ,eAAe;AACzC,YAAM,OAAO,IAAI,eAA6B;AAC9C,WAAK,MAAM,UAAU,KAAK,CAAC,CAAC;AAC5B,WAAK,MAAM,UAAU,KAAK,CAAC,CAAC;IAC9B;AACA,QAAI,QAAQ,QAAW;AAErB,MAAAA,QAAO,GAAG;AACV,YAAM,MAAM,IAAI,WAAW,KAAK,QAAQ;AACxC,UAAI,IAAI,GAAG;AACX,WAAK,OAAO,GAAG;IACjB;EACF;EACU,MAAG;AACX,UAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AAC3C,WAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACxC;;EAEU,IACR,IAAY,IAAY,IAAY,IAAY,IAAY,IAAY,IAAY,IAAU;AAE9F,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;EACjB;EACU,SAAS,KAAkB,QAAgB,QAAe;AAClE,UAAM,EAAE,GAAG,EAAC,IAAS,QAAQ,OAAO,KAAK,MAAM,CAAC;AAEhD,UAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAG,IAC1E,SACE,QAAQ,QAAQ,KAAK,IACrB,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IACpE,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAExH,SAAK,MAAM,KAAK;AAChB,SAAK,MAAM,KAAK;AAChB,SAAK,MAAM,KAAK;AAChB,SAAK,MAAM,KAAK;AAChB,SAAK,MAAM,KAAK;AAChB,SAAK,MAAM,KAAK;AAChB,SAAK,MAAM,KAAK;AAChB,SAAK,MAAM,KAAK;EAClB;EACA,UAAO;AACL,SAAK,YAAY;AACjB,UAAM,KAAK,QAAQ;AACnB,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACjC;;AAQK,IAAM,UAAkC,gBAC7C,CAAC,SAAS,IAAI,QAAQ,IAAI,CAAC;;;AC3dtB,IAAMC,WAAsB;;;ACDnC,IAAM,eAAe;AACrB,IAAM,eAAe;AAEd,IAAM,oBAAN,MAAM,mBAAkB;EAC9B,OAAO,aAAa,KAAa,mBAAmB,OAAgB;AACnE,QAAI,QAAQ,WAAW;AACtB,aAAO,EAAE,SAAS,KAAK;IACxB,WAAW,QAAQ,QAAQ;AAC1B,aAAO,EAAE,MAAM,KAAK;IACrB,WAAW,QAAQ,MAAM;AACxB,aAAO,EAAE,IAAI,KAAK;IACnB,WAAW,QAAQ,OAAO;AACzB,aAAO,EAAE,KAAK,KAAK;IACpB,WAAW,QAAQ,OAAO;AACzB,aAAO,EAAE,KAAK,KAAK;IACpB,WAAW,QAAQ,OAAO;AACzB,aAAO,EAAE,KAAK,KAAK;IACpB,WAAW,QAAQ,QAAQ;AAC1B,aAAO,EAAE,MAAM,KAAK;IACrB,WAAW,QAAQ,QAAQ;AAC1B,aAAO,EAAE,MAAM,KAAK;IACrB,WAAW,QAAQ,UAAU;AAC5B,aAAO,EAAE,QAAQ,KAAK;IACvB;AAEA,UAAM,cAAc,IAAI,MAAM,YAAY;AAC1C,QAAI,aAAa;AAChB,aAAO;QACN,QAAQ,mBAAkB,aAAa,YAAY,CAAC,GAAG,gBAAgB;MACxE;IACD;AAEA,UAAM,cAAc,IAAI,MAAM,YAAY;AAC1C,QAAI,aAAa;AAChB,YAAM,UAAU,mBAAmB,oBAAoB,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC;AACtF,aAAO;QACN,QAAQ;UACP;UACA,QAAQ,YAAY,CAAC;UACrB,MAAM,YAAY,CAAC;UACnB,YACC,YAAY,CAAC,MAAM,SAChB,CAAC,IACD,mBAAkB,oBAAoB,YAAY,CAAC,GAAG,gBAAgB;QAC3E;MACD;IACD;AAEA,UAAM,IAAI,MAAM,2DAA2D,GAAG,EAAE;EACjF;EAEA,OAAO,oBAAoB,KAAa,mBAAmB,OAAkB;AAC5E,WAAO,uBAAuB,GAAG,EAAE;MAAI,CAAC,QACvC,mBAAkB,aAAa,KAAK,gBAAgB;IACrD;EACD;EAEA,OAAO,YAAY,KAAsB;AACxC,QAAI,UAAU,KAAK;AAClB,aAAO;IACR;AACA,QAAI,QAAQ,KAAK;AAChB,aAAO;IACR;AACA,QAAI,SAAS,KAAK;AACjB,aAAO;IACR;AACA,QAAI,SAAS,KAAK;AACjB,aAAO;IACR;AACA,QAAI,SAAS,KAAK;AACjB,aAAO;IACR;AACA,QAAI,UAAU,KAAK;AAClB,aAAO;IACR;AACA,QAAI,UAAU,KAAK;AAClB,aAAO;IACR;AACA,QAAI,aAAa,KAAK;AACrB,aAAO;IACR;AACA,QAAI,YAAY,KAAK;AACpB,aAAO;IACR;AACA,QAAI,YAAY,KAAK;AACpB,aAAO,UAAU,mBAAkB,YAAY,IAAI,MAAM,CAAC;IAC3D;AACA,QAAI,YAAY,KAAK;AACpB,YAAM,SAAS,IAAI;AACnB,YAAM,aAAa,OAAO,WAAW,IAAI,mBAAkB,WAAW,EAAE,KAAK,IAAI;AACjF,aAAO,GAAG,OAAO,OAAO,KAAK,OAAO,MAAM,KAAK,OAAO,IAAI,GACzD,aAAa,IAAI,UAAU,MAAM,EAClC;IACD;AACA,UAAM,IAAI,MAAM,iBAAiB;EAClC;AACD;;;AC/FA,SAAS,WAAW,SAAkC;AACrD,SAAO,IACL,IAAI;IACJ,MAAM;IACN,GAAI;EACL,CAAC,EACA,UAAU;IACV,OAAO,CAAC,QAAyB;IACjC,QAAQ,CAAC,QAAQ,OAAO,GAAG;EAC5B,CAAC;AACH;AAEA,SAAS,WAAwC,MAAS;AACzD,SAAO,IAAI,KAAK,UAAU;IACzB,MAAM;IACN,MAAM;EACP,CAAC;AACF;AAEO,IAAM,UAAU,IAAI,MAAM,kBAAkB,EAAE,UAAU;EAC9D,UAAU,CAAC,QAAQ;AAClB,UAAM,UAAU,OAAO,QAAQ,WAAW,MAAM,MAAM,GAAG;AACzD,QAAI,CAAC,WAAW,CAAC,kBAAkB,oBAAoB,OAAO,CAAC,GAAG;AACjE,YAAM,IAAI,MAAM,uBAAuB,OAAO,EAAE;IACjD;EACD;EACA,OAAO,CAAC,QACP,OAAO,QAAQ,WAAW,QAAQ,oBAAoB,GAAG,CAAC,IAAI;EAC/D,QAAQ,CAAC,QAAQ,oBAAoB,MAAM,GAAG,CAAC;AAChD,CAAC;AAEM,IAAM,eAAe,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,UAAU;EAC1D,MAAM;EACN,OAAO,CAAC,UAAkB,WAAW,KAAK;EAC1C,QAAQ,CAAC,UAAU,SAAS,IAAI,WAAW,KAAK,CAAC;EACjD,UAAU,CAAC,UAAU;AACpB,QAAI,WAAW,KAAK,EAAE,WAAW,IAAI;AACpC,YAAM,IAAI,MAAM,+BAA+B;IAChD;EACD;AACD,CAAC;AAEM,IAAM,eAAe,IAAI,OAAO,gBAAgB;EACtD,UAAU;EACV,SAAS,IAAI,IAAI;EACjB,QAAQ;AACT,CAAC;AAEM,IAAM,kBAAkB,IAAI,OAAO,mBAAmB;EAC5D,UAAU;EACV,sBAAsB,IAAI,IAAI;EAC9B,SAAS,IAAI,KAAK;AACnB,CAAC;AAEM,IAAM,YAAY,IAAI,KAAK,aAAa;EAC9C,kBAAkB;EAClB,cAAc;EACd,WAAW;AACZ,CAAC;AAEM,IAAM,QAAQ,IAAI,KAAK,SAAS;EACtC,cAAc;EACd,aAAa;EACb,QAAQ,IAAI,OAAO,UAAU;IAC5B,sBAAsB,IAAI,IAAI;EAC/B,CAAC;EACD,WAAW;EACX,uBAAuB,IAAI,OAAO,yBAAyB;IAC1D,OAAO;IACP,cAAc,IAAI,IAAI;EACvB,CAAC;AACF,CAAC;AAEM,IAAM,UAAU,IAAI,KAAK,WAAW;EAC1C,MAAM,IAAI,OAAO,QAAQ;IACxB,OAAO,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,UAAU;MACrC,OAAO,CAAC,QAA8B,OAAO,QAAQ,WAAW,WAAW,GAAG,IAAI;MAClF,QAAQ,CAAC,QAAQ,SAAS,IAAI,WAAW,GAAG,CAAC;IAC9C,CAAC;EACF,CAAC;EACD,QAAQ;AACT,CAAC;AAED,IAAM,eAAkD,IAAI,KAAK,WAAW;EAC3E,MAAM;EACN,IAAI;EACJ,KAAK;EACL,MAAM;EACN,SAAS;EACT,QAAQ;EACR,QAAQ,IAAI,KAAK,MAAM,YAAY;EACnC,QAAQ,IAAI,KAAK,MAAM,SAAS;EAChC,KAAK;EACL,KAAK;EACL,MAAM;AACP,CAAC;AAEM,IAAM,UAAU,aAAa,UAAU;EAC7C,OAAO,CAAC,YACP,OAAO,YAAY,WAAW,kBAAkB,aAAa,SAAS,IAAI,IAAI;EAC/E,QAAQ,CAAC,YAAyB,kBAAkB,YAAY,OAAO;AACxE,CAAC;AAEM,IAAM,WAAW,IAAI,KAAK,YAAY;EAC5C,SAAS;EACT,OAAO,IAAI,IAAI;EACf,QAAQ,IAAI,IAAI;EAChB,cAAc,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;AAC/C,CAAC;AAEM,IAAM,uBAAuB,IAAI,OAAO,wBAAwB;EACtE,SAAS;EACT,QAAQ,IAAI,OAAO;EACnB,UAAU,IAAI,OAAO;EACrB,eAAe,IAAI,OAAO,OAAO;EACjC,WAAW,IAAI,OAAO,QAAQ;AAC/B,CAAC;AAEM,IAAM,UAAU,IAAI,KAAK,WAAW;;;;;;EAM1C,UAAU;;;;EAIV,iBAAiB,IAAI,OAAO,mBAAmB;IAC9C,SAAS,IAAI,OAAO,QAAQ;IAC5B,SAAS;EACV,CAAC;;;;EAID,YAAY,IAAI,OAAO,cAAc;IACpC,MAAM;IACN,SAAS,IAAI,OAAO,QAAQ;EAC7B,CAAC;;;;EAID,YAAY,IAAI,OAAO,cAAc;IACpC,aAAa;IACb,SAAS,IAAI,OAAO,QAAQ;EAC7B,CAAC;;;;EAID,SAAS,IAAI,OAAO,WAAW;IAC9B,SAAS,IAAI;MACZ,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,UAAU;QAC9B,OAAO,CAAC,QAA8B,OAAO,QAAQ,WAAW,WAAW,GAAG,IAAI;QAClF,QAAQ,CAAC,QAAQ,SAAS,IAAI,WAAW,GAAG,CAAC;MAC9C,CAAC;IACF;IACA,cAAc,IAAI,OAAO,OAAO;EACjC,CAAC;;;;;;EAMD,aAAa,IAAI,OAAO,eAAe;IACtC,MAAM,WAAW,OAAO,EAAE,UAAU;MACnC,OAAO,CAAC,QACP,QAAQ,OACL;QACA,MAAM;MACP,IACC;QACA,MAAM;MACP;MACH,QAAQ,CAAC,QAAQ,IAAI,QAAQ;IAC9B,CAAC;IACD,UAAU,IAAI,OAAO,QAAQ;EAC9B,CAAC;EACD,SAAS,IAAI,OAAO,WAAW;IAC9B,SAAS,IAAI;MACZ,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,UAAU;QAC9B,OAAO,CAAC,QAA8B,OAAO,QAAQ,WAAW,WAAW,GAAG,IAAI;QAClF,QAAQ,CAAC,QAAQ,SAAS,IAAI,WAAW,GAAG,CAAC;MAC9C,CAAC;IACF;IACA,cAAc,IAAI,OAAO,OAAO;IAChC,SAAS;IACT,QAAQ;EACT,CAAC;AACF,CAAC;AAEM,IAAM,0BAA0B,IAAI,OAAO,2BAA2B;EAC5E,QAAQ,IAAI,OAAO,OAAO;EAC1B,UAAU,IAAI,OAAO,OAAO;AAC7B,CAAC;AAEM,IAAM,kBAAkB,IAAI,KAAK,mBAAmB;EAC1D;EACA,aAAa;EACb,SAAS;EACT,yBAAyB;AAC1B,CAAC;AAEM,IAAM,wBAAwB,IAAI,KAAK,yBAAyB;EACtE,MAAM;EACN,OAAO,WAAW;AACnB,CAAC;AAEM,IAAM,YAAY,IAAI,OAAO,aAAa;EAChD,SAAS;EACT,QAAQ,IAAI,OAAO;EACnB,MAAM,IAAI,OAAO;EACjB,YAAY,IAAI,OAAO,YAAY;AACpC,CAAC;AAEM,IAAM,UAAU,IAAI,OAAO,WAAW;EAC5C,SAAS,IAAI,OAAO,YAAY;EAChC,OAAO;EACP,OAAO,IAAI,IAAI;EACf,QAAQ,IAAI,IAAI;AACjB,CAAC;AAEM,IAAM,oBAAoB,IAAI,OAAO,qBAAqB;EAChE,MAAM;EACN,QAAQ;EACR,SAAS;EACT,YAAY;AACb,CAAC;AAEM,IAAM,kBAAkB,IAAI,KAAK,mBAAmB;EAC1D,IAAI;AACL,CAAC;AAEM,IAAM,cAAc,IAAI,KAAK,eAAe;EAClD,iBAAiB;EACjB,oBAAoB;EACpB,mBAAmB;EACnB,iBAAiB;AAClB,CAAC;AAEM,IAAM,gBAAgB,IAAI,KAAK,iBAAiB;EACtD,IAAI;AACL,CAAC;AAEM,IAAM,QAAQ,IAAI,KAAK,SAAS;EACtC,KAAK;AACN,CAAC;AAEM,IAAM,SAAS,IAAI,OAAO,UAAU;EAC1C,OAAO;EACP,SAAS;EACT,OAAO;AACR,CAAC;AAEM,SAAS,cAAsC,GAAM;AAC3D,SAAO,IAAI,OAAO,iBAAiB,EAAE,IAAI,KAAK;IAC7C,QAAQ;IACR,OAAO;EACR,CAAC;AACF;AAEO,IAAM,sBAAsB,IAAI,KAAK,uBAAuB;EAClE,SAAS,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC;EACpC,WAAW,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC;EACtC,WAAW,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC;EACtC,SAAS,IAAI,OAAO,IAAI,GAAG,CAAC;EAC5B,SAAS,IAAI,OAAO,IAAI,GAAG,CAAC;AAC7B,CAAC;AAEM,IAAM,YAAY,IAAI,KAAK,aAAa;EAC9C,SAAS,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC;EACpC,WAAW,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC;EACtC,WAAW,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC;EACtC,SAAS,IAAI,OAAO,IAAI,GAAG,CAAC;EAC5B,SAAS,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC;AACrC,CAAC;AAEM,IAAM,gBAAgB,IAAI,OAAO,iBAAiB;EACxD,QAAQ;EACR,QAAQ,IAAI,GAAG;AAChB,CAAC;AAEM,IAAM,oBAAoB,IAAI,OAAO,qBAAqB;EAChE,QAAQ,IAAI,OAAO,aAAa;EAChC,WAAW,IAAI,IAAI;AACpB,CAAC;AAEM,IAAM,WAAW,IAAI,OAAO,YAAY;EAC9C,MAAM,IAAI,OAAO,mBAAmB;EACpC,QAAQ,IAAI,IAAI;EAChB,aAAa;AACd,CAAC;AAEM,IAAM,eAAe,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,UAAU;EAC1D,OAAO,CAAC,QAA8B,OAAO,QAAQ,WAAW,WAAW,GAAG,IAAI;EAClF,QAAQ,CAAC,QAAQ,SAAS,IAAI,WAAW,GAAG,CAAC;AAC9C,CAAC;AAEM,IAAM,0BAA0B,IAAI,OAAO,2BAA2B;EAC5E,eAAe,cAAc,eAAe;EAC5C,cAAc,IAAI,OAAO,YAAY;AACtC,CAAC;AAEM,IAAM,mBAAmB,IAAI,OAAO,yBAAyB;EACnE,MAAM;AACP,CAAC;AAEM,IAAM,uBAAuB,IAAI,OAAO,wBAAwB;EACtE,mBAAmB,IAAI,OAAO,IAAI,GAAG,CAAC;EACtC,gBAAgB,IAAI,OAAO;EAC3B,eAAe,IAAI,OAAO,IAAI,GAAG,CAAC;AACnC,CAAC;;;ACzTD,IAAM,sBAAsB,IAAI,KAAK,uBAAuB;EAC3D,sBAAsB,IAAI,OAAO,wBAAwB,EAAE,WAAW,QAAQ,CAAC;EAC/E,aAAa,IAAI,OAAO,eAAe,EAAE,UAAU,QAAQ,CAAC;EAC5D,qBAAqB;EACrB,oBAAoB,IAAI,OAAO,sBAAsB,EAAE,QAAQ,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,CAAC;EACrF,sBAAsB,IAAI,OAAO,wBAAwB,EAAE,QAAQ,IAAI,GAAG,EAAE,CAAC;EAC7E,uBAAuB,IAAI,OAAO,yBAAyB;IAC1D,WAAW;IACX,UAAU;EACX,CAAC;AACF,CAAC;AAED,IAAM,WAAW,IAAI,OAAO,YAAY;EACvC,SAAS;EACT,MAAM,IAAI,OAAO;AAClB,CAAC;AACD,IAAM,eAAe,IAAI,OAAO,gBAAgB;EAC/C,QAAQ;EACR,UAAU,IAAI,IAAI;EAClB,aAAa,IAAI,IAAI;EACrB,cAAc,IAAI,OAAO,IAAI,OAAO,CAAC;AACtC,CAAC;AAED,IAAM,uBAAuB,IAAI,KAAK,wBAAwB;EAC7D,cAAc;EACd,iBAAiB;EACjB,uBAAuB;EACvB,uCAAuC;EACvC,kBAAkB,IAAI,OAAO,oBAAoB,EAAE,KAAK,IAAI,IAAI,EAAE,CAAC;EACnE,2BAA2B,IAAI,OAAO,6BAA6B;IAClE,WAAW,IAAI,IAAI;IACnB,cAAc,IAAI,IAAI;EACvB,CAAC;EACD,oBAAoB,IAAI,OAAO,sBAAsB,EAAE,WAAW,IAAI,IAAI,EAAE,CAAC;EAC7E,qBAAqB;EACrB,mBAAmB;EACnB,sBAAsB;EACtB,uBAAuB;EACvB,iCAAiC;AAClC,CAAC;AAED,IAAM,oBAAoB,IAAI,KAAK,qBAAqB;EACvD,cAAc;EACd,wBAAwB;AACzB,CAAC;AAED,IAAM,yBAAyB,IAAI,KAAK,0BAA0B;EACjE,iBAAiB;EACjB,kBAAkB;EAClB,oBAAoB;EACpB,wBAAwB;EACxB,kBAAkB,IAAI,OAAO,oBAAoB;IAChD,YAAY,IAAI,IAAI;IACpB,eAAe,IAAI,IAAI;EACxB,CAAC;EACD,mBAAmB,IAAI,OAAO,qBAAqB;IAClD,YAAY,IAAI,IAAI;IACpB,eAAe,IAAI,IAAI;EACxB,CAAC;EACD,yBAAyB,IAAI,OAAO,2BAA2B,EAAE,QAAQ,QAAQ,CAAC;EAClF,yBAAyB;EACzB,qBAAqB;EACrB,4BAA4B;EAC5B,0BAA0B;EAC1B,2BAA2B,IAAI,OAAO,YAAY;EAClD,WAAW,IAAI,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,CAAC;EAC9C,sCAAsC;EACtC,sBAAsB;EACtB,kBAAkB;EAClB,eAAe;EACf,mBAAmB;EACnB,yBAAyB;EACzB,sBAAsB,IAAI,OAAO,wBAAwB;IACxD,QAAQ,IAAI,IAAI;IAChB,MAAM;EACP,CAAC;EACD,mBAAmB,IAAI,OAAO,qBAAqB;IAClD,aAAa,IAAI,IAAI;IACrB,MAAM;EACP,CAAC;EACD,wBAAwB,IAAI,OAAO,0BAA0B;IAC5D,WAAW,IAAI,IAAI;IACnB,cAAc,IAAI,IAAI;EACvB,CAAC;EACD,iCAAiC,IAAI,OAAO,mCAAmC;IAC9E,KAAK,IAAI,IAAI;EACd,CAAC;EACD,uBAAuB;EACvB,iBAAiB,IAAI,OAAO,mBAAmB,EAAE,aAAa,IAAI,IAAI,GAAG,SAAS,IAAI,IAAI,EAAE,CAAC;EAC7F,iCAAiC;EACjC,mCAAmC;EACnC,qBAAqB,IAAI,OAAO,uBAAuB,EAAE,cAAc,oBAAoB,CAAC;EAC5F,wBAAwB,IAAI,OAAO,0BAA0B;IAC5D,aAAa,IAAI,IAAI;IACrB,SAAS,IAAI,IAAI;EAClB,CAAC;EACD,mBAAmB;EACnB,6BAA6B;EAC7B,iCAAiC;EACjC,oBAAoB;EACpB,+CAA+C,IAAI;IAClD;IACA;MACC,kBAAkB,IAAI,OAAO,OAAO;IACrC;EACD;EACA,sBAAsB,IAAI,OAAO,wBAAwB;IACxD,SAAS;IACT,UAAU,IAAI,OAAO;EACtB,CAAC;EACD,qBAAqB,IAAI,OAAO,uBAAuB,EAAE,UAAU,IAAI,OAAO,EAAE,CAAC;EACjF,8CAA8C;AAC/C,CAAC;AAED,IAAM,kBAAkB,IAAI,KAAK,mBAAmB;EACnD,SAAS;EACT,QAAQ,IAAI,OAAO,mBAAmB;IACrC,OAAO;IACP,SAAS,IAAI,OAAO,IAAI,IAAI,CAAC;EAC9B,CAAC;AACF,CAAC;AAED,IAAM,iBAAiB,IAAI,OAAO,kBAAkB;EACnD,iBAAiB,IAAI,IAAI;EACzB,aAAa,IAAI,IAAI;EACrB,eAAe,IAAI,IAAI;EACvB,yBAAyB,IAAI,IAAI;AAClC,CAAC;AAED,IAAM,uBAAuB,IAAI,OAAO,wBAAwB;EAC/D,QAAQ;EACR,eAAe,IAAI,IAAI;EACvB,SAAS;EACT,oBAAoB,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;EAC9D,eAAe,IAAI,OAAO,YAAY;EACtC,mBAAmB;EACnB,SAAS,IAAI,OAAO,IAAI,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC;EACpD,SAAS,IAAI,OAAO,IAAI,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC;EACpD,WAAW,IAAI,OAAO,IAAI,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC;EACtD,SAAS,IAAI,OAAO,YAAY;EAChC,sBAAsB,IAAI,OAAO,YAAY;EAC7C,SAAS,IAAI,OAAO,YAAY;EAChC,WAAW,IAAI,MAAM,CAAC,cAAc,KAAK,CAAC;EAC1C,cAAc,IAAI,OAAO,YAAY;EACrC,cAAc,IAAI,OAAO,YAAY;AACtC,CAAC;AAED,IAAM,gBAAgB,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,YAAY,CAAC;AAEzD,IAAM,WAAW,IAAI,KAAK,YAAY;EACrC,UAAU;EACV,OAAO,IAAI,MAAM,CAAC,eAAe,KAAK,CAAC;AACxC,CAAC;AAED,IAAM,YAAY,IAAI,KAAK,aAAa;EACvC,UAAU;EACV,aAAa,IAAI,MAAM,CAAC,cAAc,KAAK,CAAC;EAC5C,cAAc;AACf,CAAC;AAED,IAAM,cAAc,IAAI,KAAK,eAAe;EAC3C,MAAM;EACN,SAAS;EACT,SAAS;AACV,CAAC;AAED,IAAM,sBAAsB,IAAI,OAAO,uBAAuB;EAC7D,YAAY;EACZ,aAAa;EACb,aAAa;AACd,CAAC;AAED,IAAM,sBAAsB,IAAI,KAAK,uBAAuB;EAC3D,cAAc;EACd,eAAe,IAAI,IAAI;EACvB,aAAa,IAAI,IAAI;EACrB,WAAW,IAAI,IAAI;EACnB,gBAAgB;AACjB,CAAC;AAED,IAAM,uBAAuB,IAAI,OAAO,wBAAwB;EAC/D,QAAQ;EACR,eAAe,IAAI,IAAI;EACvB,SAAS;EACT,mBAAmB;EACnB,gBAAgB,IAAI,OAAO,IAAI,IAAI,CAAC;EACpC,cAAc,IAAI,OAAO,YAAY;EACrC,cAAc,IAAI,OAAO,YAAY;EACrC,gBAAgB,IAAI,IAAI;EACxB,gBAAgB,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,mBAAmB,CAAC,CAAC;EACpE,wBAAwB,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,mBAAmB,CAAC,CAAC;EAC5E,eAAe,IAAI,OAAO,YAAY;AACvC,CAAC;AAEM,IAAM,qBAAqB,IAAI,KAAK,sBAAsB;EAChE,IAAI;EACJ,IAAI;AACL,CAAC;;;AC/JD,IAAM,SAAS;EACd,GAAG;EACH,IAAI,IAAI,GAAG;EACX,KAAK,IAAI,IAAI;EACb,KAAK,IAAI,IAAI;EACb,KAAK,IAAI,IAAI;EACb,MAAM,IAAI,KAAK;EACf,MAAM,IAAI,KAAK;EACf,SAAS,IAAI,QAAQ;EACrB,MAAM,IAAI,KAAK;EACf,QAAQ,IAAI,OAAO;EACnB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACD;;;AC/EO,SAAS,qBACf,UACA,SACA,KACC;AACD,QAAM,UAAU,OAAI,QAAQ,UAAU,QAAQ,EAAE,QAAQ;AACxD,QAAM,MAAM,OAAI,QAAQ,UAAU,OAAO,EAAE,QAAQ;AACnD,QAAM,YAAY,OAAI,IAAI,EAAE,UAAU,IAAI,MAAM,EAAE,QAAQ;AAE1D,QAAM,OAAOC,SAAQ,OAAO;IAC3B,OAAO;EACR,CAAC;AAED,OAAK,OAAO,IAAI,WAAW,CAAC,GAAI,CAAC,CAAC;AAClC,OAAK,OAAO,OAAO;AACnB,OAAK,OAAO,SAAS;AACrB,OAAK,OAAO,GAAG;AACf,OAAK,OAAO,GAAG;AAEf,SAAO,KAAK,MAAM,KAAK,OAAO,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;AAC9C;", "names": ["padding", "alphabet", "base64String", "chunk", "_BcsType", "options", "hex", "toBytes", "hex", "name", "isBytes", "anumber", "abytes", "hasHexBuiltin", "abytes", "hex", "abytes", "isLE", "_32n", "abytes", "anumber", "abytes", "blake2b", "blake2b"]}