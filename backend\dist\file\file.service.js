"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var FileService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileService = void 0;
const common_1 = require("@nestjs/common");
const sui_service_1 = require("../sui/sui.service");
const auth_service_1 = require("../auth/auth.service");
const walrus_service_1 = require("../storage/walrus/walrus.service");
const seal_service_1 = require("../storage/seal/seal.service");
const access_control_service_1 = require("../access-control/access-control.service");
let FileService = FileService_1 = class FileService {
    suiService;
    authService;
    walrusService;
    sealService;
    accessControlService;
    logger = new common_1.Logger(FileService_1.name);
    uploadedFiles = new Map();
    testModeFiles = [];
    constructor(suiService, authService, walrusService, sealService, accessControlService) {
        this.suiService = suiService;
        this.authService = authService;
        this.walrusService = walrusService;
        this.sealService = sealService;
        this.accessControlService = accessControlService;
    }
    async uploadFile(token, uploadRequest) {
        try {
            const user = await this.authService.verifyToken(token);
            if (!user) {
                return {
                    success: false,
                    fileCid: '',
                    transactionDigest: '',
                    walrusCid: uploadRequest.walrusCid || '',
                    message: 'Authentication failed',
                };
            }
            let walrusCid = uploadRequest.walrusCid;
            let encryptionKeys;
            let dataToUpload = uploadRequest.fileData;
            if (uploadRequest.enableEncryption) {
                this.logger.log(`Encrypting file with SEAL: ${uploadRequest.filename}`);
                const packageId = process.env.SUI_PACKAGE_ID || '0x1';
                const encryptionResult = await this.sealService.encryptFile(uploadRequest.fileData, {
                    packageId,
                    identity: uploadRequest.filename,
                    threshold: 3,
                });
                if (!encryptionResult.success) {
                    return {
                        success: false,
                        fileCid: '',
                        transactionDigest: '',
                        walrusCid: '',
                        message: `Failed to encrypt file: ${encryptionResult.error}`,
                    };
                }
                dataToUpload = Buffer.from(encryptionResult.encryptedData);
                encryptionKeys = {
                    encryptionId: encryptionResult.encryptionId,
                    symmetricKey: Buffer.from(encryptionResult.symmetricKey).toString('base64'),
                };
                this.logger.log(`File encrypted successfully: ${uploadRequest.filename}`);
            }
            if (!walrusCid) {
                this.logger.log(`Uploading file to Walrus: ${uploadRequest.filename}${uploadRequest.enableEncryption ? ' (encrypted)' : ''}`);
                let walrusResult;
                if (user.ephemeralKeyPair && user.zkLoginProof && user.jwt && user.userSalt) {
                    this.logger.log('Using zkLogin signature for Walrus upload');
                    const zkLoginParams = {
                        ephemeralKeyPair: user.ephemeralKeyPair,
                        zkLoginProof: user.zkLoginProof,
                        jwt: user.jwt,
                        userSalt: user.userSalt,
                    };
                    walrusResult = await this.walrusService.uploadFileWithZkLogin(dataToUpload, uploadRequest.filename, zkLoginParams, uploadRequest.contentType);
                }
                else {
                    this.logger.warn('zkLogin parameters not available, falling back to regular upload');
                    walrusResult = await this.walrusService.uploadFile(dataToUpload, uploadRequest.filename, uploadRequest.contentType);
                }
                if (!walrusResult.success) {
                    return {
                        success: false,
                        fileCid: '',
                        transactionDigest: '',
                        walrusCid: '',
                        message: `Failed to upload to Walrus: ${walrusResult.error}`,
                    };
                }
                walrusCid = walrusResult.blobId;
                this.logger.log(`File uploaded to Walrus with CID: ${walrusCid}`);
            }
            if (!walrusCid) {
                return {
                    success: false,
                    fileCid: '',
                    transactionDigest: '',
                    walrusCid: '',
                    message: 'Failed to get Walrus CID',
                };
            }
            const transactionDigest = await this.suiService.uploadFile(user.zkLoginAddress, walrusCid, uploadRequest.filename, uploadRequest.fileSize);
            this.logger.log(`File metadata uploaded to smart contract: ${uploadRequest.filename} by ${user.zkLoginAddress}`);
            const userFiles = this.uploadedFiles.get(user.zkLoginAddress) || [];
            userFiles.push({
                cid: walrusCid,
                filename: uploadRequest.filename,
                fileSize: uploadRequest.fileSize,
                uploadTimestamp: Date.now(),
                uploader: user.zkLoginAddress,
                isOwner: true,
                isEncrypted: uploadRequest.enableEncryption,
                encryptionKeys: encryptionKeys ? {
                    publicKey: encryptionKeys.encryptionId,
                    secretKey: encryptionKeys.symmetricKey,
                } : undefined,
            });
            this.uploadedFiles.set(user.zkLoginAddress, userFiles);
            return {
                success: true,
                fileCid: walrusCid,
                transactionDigest,
                walrusCid,
                message: 'File uploaded successfully',
            };
        }
        catch (error) {
            this.logger.error('Failed to upload file', error);
            return {
                success: false,
                fileCid: '',
                transactionDigest: '',
                walrusCid: uploadRequest.walrusCid || '',
                message: `Failed to upload file: ${error.message}`,
            };
        }
    }
    async accessFile(token, accessRequest) {
        try {
            const user = await this.authService.verifyToken(token);
            if (!user) {
                return {
                    success: false,
                    authorized: false,
                    message: 'Authentication failed',
                };
            }
            const isAuthorized = await this.authService.isUserAuthorizedForFile(token, accessRequest.fileCid);
            const accessControlResult = await this.accessControlService.validateAccess(token, {
                fileCid: accessRequest.fileCid,
                userAddress: user.zkLoginAddress,
                userEmail: user.email,
            });
            if (!isAuthorized || !accessControlResult.accessGranted) {
                const reason = !isAuthorized
                    ? 'User not authorized for this file'
                    : accessControlResult.message || 'Access control rules not met';
                return {
                    success: true,
                    authorized: false,
                    message: `Access denied: ${reason}`,
                };
            }
            const fileMetadata = await this.suiService.getFileMetadata(accessRequest.fileCid);
            if (!fileMetadata) {
                return {
                    success: false,
                    authorized: true,
                    message: 'File not found',
                };
            }
            this.logger.log(`File access granted: ${accessRequest.fileCid} for ${user.zkLoginAddress}`);
            return {
                success: true,
                authorized: true,
                fileMetadata: {
                    filename: fileMetadata.filename,
                    fileSize: fileMetadata.fileSize,
                    uploadTimestamp: fileMetadata.uploadTimestamp,
                    uploader: fileMetadata.uploader,
                },
                walrusCid: accessRequest.fileCid,
                message: 'Access granted',
            };
        }
        catch (error) {
            this.logger.error('Failed to check file access', error);
            return {
                success: false,
                authorized: false,
                message: `Failed to check file access: ${error.message}`,
            };
        }
    }
    async grantFileAccess(token, fileCid, recipientAddress) {
        try {
            const user = await this.authService.verifyToken(token);
            if (!user) {
                return {
                    success: false,
                    message: 'Authentication failed',
                };
            }
            const transactionDigest = await this.suiService.grantFileAccess(user.zkLoginAddress, fileCid, recipientAddress);
            this.logger.log(`Access granted: ${fileCid} from ${user.zkLoginAddress} to ${recipientAddress}`);
            return {
                success: true,
                message: 'Access granted successfully',
                transactionDigest,
            };
        }
        catch (error) {
            this.logger.error('Failed to grant file access', error);
            return {
                success: false,
                message: `Failed to grant access: ${error.message}`,
            };
        }
    }
    async revokeFileAccess(token, fileCid, addressToRemove) {
        try {
            const user = await this.authService.verifyToken(token);
            if (!user) {
                return {
                    success: false,
                    message: 'Authentication failed',
                };
            }
            const transactionDigest = await this.suiService.revokeFileAccess(user.zkLoginAddress, fileCid, addressToRemove);
            this.logger.log(`Access revoked: ${fileCid} by ${user.zkLoginAddress} for ${addressToRemove}`);
            return {
                success: true,
                message: 'Access revoked successfully',
                transactionDigest,
            };
        }
        catch (error) {
            this.logger.error('Failed to revoke file access', error);
            return {
                success: false,
                message: `Failed to revoke access: ${error.message}`,
            };
        }
    }
    async downloadFile(token, fileCid) {
        try {
            const accessResult = await this.accessFile(token, { fileCid });
            if (!accessResult.success || !accessResult.authorized) {
                return {
                    success: false,
                    message: accessResult.message,
                };
            }
            const downloadResult = await this.walrusService.downloadFile(fileCid);
            if (!downloadResult.success) {
                return {
                    success: false,
                    message: `Failed to download from Walrus: ${downloadResult.error}`,
                };
            }
            let fileData = Buffer.from(downloadResult.data);
            let isEncrypted = false;
            try {
                const dataStr = new TextDecoder('utf-8', { fatal: true }).decode(downloadResult.data);
                try {
                    const metadata = JSON.parse(dataStr);
                    if (metadata.chunks && metadata.scheme === 'BFV') {
                        this.logger.warn(`Detected old Microsoft SEAL encrypted file: ${fileCid} - not supported`);
                    }
                }
                catch {
                }
            }
            catch (error) {
                isEncrypted = true;
                this.logger.log(`Detected potential Mysten SEAL encrypted file: ${fileCid}`);
            }
            this.logger.log(`File downloaded successfully: ${fileCid}${isEncrypted ? ' (encrypted)' : ''}`);
            return {
                success: true,
                fileData,
                filename: accessResult.fileMetadata?.filename,
                contentType: 'application/octet-stream',
                message: 'File downloaded successfully',
                isEncrypted,
            };
        }
        catch (error) {
            this.logger.error(`Failed to download file ${fileCid}:`, error);
            return {
                success: false,
                message: `Failed to download file: ${error.message}`,
            };
        }
    }
    async downloadAndDecryptFile(token, fileCid, sessionKey, txBytes) {
        try {
            const downloadResult = await this.downloadFile(token, fileCid);
            if (!downloadResult.success) {
                return downloadResult;
            }
            if (!downloadResult.isEncrypted) {
                return {
                    success: false,
                    message: 'File is not encrypted',
                };
            }
            this.logger.log(`Decrypting file with Mysten SEAL: ${fileCid}`);
            const decryptionResult = await this.sealService.decryptFile(new Uint8Array(downloadResult.fileData), sessionKey, txBytes);
            if (!decryptionResult.success) {
                return {
                    success: false,
                    message: `Failed to decrypt file: ${decryptionResult.error}`,
                };
            }
            this.logger.log(`File decrypted successfully: ${fileCid}`);
            return {
                success: true,
                fileData: Buffer.from(decryptionResult.decryptedData),
                filename: downloadResult.filename,
                contentType: downloadResult.contentType,
                message: 'File downloaded and decrypted successfully',
            };
        }
        catch (error) {
            this.logger.error(`Failed to download and decrypt file ${fileCid}:`, error);
            return {
                success: false,
                message: `Failed to download and decrypt file: ${error.message}`,
            };
        }
    }
    async downloadEncryptedFile(token, fileCid, symmetricKey) {
        this.logger.warn('downloadEncryptedFile is deprecated for Mysten SEAL. Use downloadAndDecryptFile instead.');
        return {
            success: false,
            message: 'This method is deprecated for Mysten SEAL. Use downloadAndDecryptFile with SessionKey and transaction bytes.',
        };
    }
    async uploadFileNoAuth(uploadRequest) {
        try {
            this.logger.log(`Uploading file without auth: ${uploadRequest.filename}`);
            let walrusCid = uploadRequest.walrusCid;
            let dataToUpload = uploadRequest.fileData;
            if (uploadRequest.enableEncryption) {
                this.logger.log(`Encrypting file with SEAL: ${uploadRequest.filename}`);
                const packageId = process.env.SUI_PACKAGE_ID || '0x1';
                const encryptionResult = await this.sealService.encryptFile(uploadRequest.fileData, {
                    packageId,
                    identity: uploadRequest.filename,
                    threshold: 3,
                });
                if (!encryptionResult.success) {
                    return {
                        success: false,
                        fileCid: '',
                        transactionDigest: '',
                        walrusCid: '',
                        message: `Failed to encrypt file: ${encryptionResult.error}`,
                    };
                }
                dataToUpload = Buffer.from(encryptionResult.encryptedData);
                this.logger.log(`File encrypted successfully: ${uploadRequest.filename}`);
            }
            if (!walrusCid) {
                this.logger.log(`Uploading file to Walrus: ${uploadRequest.filename}${uploadRequest.enableEncryption ? ' (encrypted)' : ''}`);
                const walrusResult = await this.walrusService.uploadFile(dataToUpload, uploadRequest.filename, uploadRequest.contentType);
                if (!walrusResult.success) {
                    return {
                        success: false,
                        fileCid: '',
                        transactionDigest: '',
                        walrusCid: '',
                        message: `Failed to upload to Walrus: ${walrusResult.error}`,
                    };
                }
                walrusCid = walrusResult.blobId;
                this.logger.log(`File uploaded to Walrus with CID: ${walrusCid}`);
            }
            const mockTransactionDigest = `mock_tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            this.logger.log(`File upload completed (no-auth mode): ${uploadRequest.filename} -> ${walrusCid}`);
            this.testModeFiles.push({
                cid: walrusCid,
                filename: uploadRequest.filename,
                fileSize: uploadRequest.fileSize,
                uploadTimestamp: Date.now(),
                uploader: 'test-user',
                isOwner: true,
                isEncrypted: uploadRequest.enableEncryption,
                encryptionKeys: undefined,
            });
            return {
                success: true,
                fileCid: walrusCid,
                transactionDigest: mockTransactionDigest,
                walrusCid,
                message: 'File uploaded successfully (no-auth mode)',
            };
        }
        catch (error) {
            this.logger.error('Failed to upload file (no-auth)', error);
            return {
                success: false,
                fileCid: '',
                transactionDigest: '',
                walrusCid: uploadRequest.walrusCid || '',
                message: `Failed to upload file: ${error.message}`,
            };
        }
    }
    async downloadFileNoAuth(fileCid) {
        try {
            this.logger.log(`Downloading file without auth: ${fileCid}`);
            const downloadResult = await this.walrusService.downloadFile(fileCid);
            if (!downloadResult.success) {
                return {
                    success: false,
                    message: `Failed to download from Walrus: ${downloadResult.error}`,
                };
            }
            this.logger.log(`File downloaded successfully: ${fileCid}`);
            return {
                success: true,
                fileData: Buffer.from(downloadResult.data),
                filename: `file_${fileCid.substring(0, 8)}.bin`,
                contentType: 'application/octet-stream',
                message: 'File downloaded successfully',
            };
        }
        catch (error) {
            this.logger.error(`Failed to download file ${fileCid}:`, error);
            return {
                success: false,
                message: `Failed to download file: ${error.message}`,
            };
        }
    }
    async listUserFiles(token) {
        try {
            const user = await this.authService.verifyToken(token);
            if (!user) {
                return {
                    success: false,
                    files: [],
                    message: 'Authentication failed',
                };
            }
            this.logger.log(`Listing files for user ${user.zkLoginAddress}`);
            const userFiles = this.uploadedFiles.get(user.zkLoginAddress) || [];
            return {
                success: true,
                files: userFiles,
                message: userFiles.length > 0 ? `Found ${userFiles.length} files` : 'No files uploaded yet',
            };
        }
        catch (error) {
            this.logger.error('Failed to list user files', error);
            return {
                success: false,
                files: [],
                message: `Failed to list files: ${error.message}`,
            };
        }
    }
    async listUserFilesNoAuth() {
        try {
            this.logger.log('Listing files (no auth - test mode)');
            return {
                success: true,
                files: this.testModeFiles,
                message: this.testModeFiles.length > 0 ? `Found ${this.testModeFiles.length} files` : 'No files uploaded yet',
            };
        }
        catch (error) {
            this.logger.error('Failed to list user files (no auth)', error);
            return {
                success: false,
                files: [],
                message: `Failed to list files: ${error.message}`,
            };
        }
    }
    async clearUserFiles(token) {
        try {
            const user = await this.authService.verifyToken(token);
            if (!user) {
                return {
                    success: false,
                    message: 'Authentication failed',
                };
            }
            this.logger.log(`Clearing files for user ${user.zkLoginAddress}`);
            const userFiles = this.uploadedFiles.get(user.zkLoginAddress) || [];
            const fileCount = userFiles.length;
            this.uploadedFiles.delete(user.zkLoginAddress);
            return {
                success: true,
                message: `Cleared ${fileCount} files for user`,
            };
        }
        catch (error) {
            this.logger.error('Failed to clear user files', error);
            return {
                success: false,
                message: `Failed to clear files: ${error.message}`,
            };
        }
    }
    async clearUserFilesNoAuth() {
        try {
            this.logger.log('Clearing files (no auth - test mode)');
            const fileCount = this.testModeFiles.length;
            this.testModeFiles = [];
            return {
                success: true,
                message: `Cleared ${fileCount} files from test mode`,
            };
        }
        catch (error) {
            this.logger.error('Failed to clear user files (no auth)', error);
            return {
                success: false,
                message: `Failed to clear files: ${error.message}`,
            };
        }
    }
    async downloadSharedFile(shareId, token) {
        try {
            this.logger.log(`Downloading shared file: ${shareId}`);
            const shareValidation = await this.accessControlService.validateShareLink(shareId, token);
            if (!shareValidation.success) {
                return {
                    success: false,
                    message: shareValidation.message,
                };
            }
            const fileCid = shareValidation.data?.fileCid;
            if (!fileCid) {
                return {
                    success: false,
                    message: 'File not found in share link',
                };
            }
            const downloadResult = await this.walrusService.downloadFile(fileCid);
            if (!downloadResult.success) {
                return {
                    success: false,
                    message: `Failed to download from Walrus: ${downloadResult.error}`,
                };
            }
            if (!downloadResult.data) {
                return {
                    success: false,
                    message: 'No file data received from Walrus',
                };
            }
            let fileData = Buffer.from(downloadResult.data);
            let isEncrypted = false;
            try {
                const dataStr = new TextDecoder('utf-8', { fatal: true }).decode(downloadResult.data);
                try {
                    const metadata = JSON.parse(dataStr);
                    if (metadata.chunks && metadata.scheme === 'BFV') {
                        this.logger.warn(`Detected old Microsoft SEAL encrypted file: ${fileCid} - not supported`);
                        isEncrypted = true;
                    }
                }
                catch {
                }
            }
            catch (error) {
                isEncrypted = true;
                this.logger.log(`Detected potential Mysten SEAL encrypted file: ${fileCid}`);
            }
            let filename = shareValidation.data?.filename || `shared-file-${fileCid.substring(0, 8)}`;
            let foundInMemory = false;
            this.logger.log(`Looking up file metadata for fileCid: ${fileCid}`);
            this.logger.log(`Initial filename from shareValidation: ${shareValidation.data?.filename || 'none'}`);
            {
                this.logger.log(`Checking ${this.uploadedFiles.size} user file collections in memory`);
                for (const [userAddress, userFiles] of this.uploadedFiles.entries()) {
                    this.logger.log(`Checking user ${userAddress} with ${userFiles.length} files`);
                    const foundFile = userFiles.find(file => file.cid === fileCid);
                    if (foundFile) {
                        filename = foundFile.filename.replace(/\.encrypted$/, '');
                        isEncrypted = foundFile.isEncrypted || isEncrypted;
                        foundInMemory = true;
                        this.logger.log(`✅ Found file in user storage: ${foundFile.filename} -> ${filename} (encrypted: ${isEncrypted})`);
                        break;
                    }
                }
                this.logger.log(`Checking ${this.testModeFiles.length} test mode files`);
                const testFile = this.testModeFiles.find(file => file.cid === fileCid);
                if (testFile && !foundInMemory) {
                    filename = testFile.filename.replace(/\.encrypted$/, '');
                    isEncrypted = testFile.isEncrypted || isEncrypted;
                    foundInMemory = true;
                    this.logger.log(`✅ Found file in test storage: ${testFile.filename} -> ${filename} (encrypted: ${isEncrypted})`);
                }
                if (!foundInMemory) {
                    this.logger.warn(`❌ File not found in memory storage for CID: ${fileCid}`);
                    this.logger.log(`Available user files:`, Array.from(this.uploadedFiles.entries()).map(([user, files]) => ({ user, files: files.map(f => ({ cid: f.cid, filename: f.filename })) })));
                    this.logger.log(`Available test files:`, this.testModeFiles.map(f => ({ cid: f.cid, filename: f.filename })));
                }
            }
            const contentType = shareValidation.data?.contentType || 'application/octet-stream';
            let message = 'File downloaded successfully';
            if (isEncrypted) {
                message = 'Encrypted file downloaded - this file was encrypted during upload and may require the file owner to provide decryption access';
                this.logger.warn(`Downloaded encrypted file: ${filename} - automatic decryption not available for shared files`);
            }
            this.logger.log(`Shared file downloaded successfully: ${shareId} -> ${filename}${isEncrypted ? ' (encrypted)' : ''}`);
            return {
                success: true,
                fileData,
                filename,
                contentType,
                message,
                isEncrypted,
            };
        }
        catch (error) {
            this.logger.error(`Failed to download shared file ${shareId}:`, error);
            return {
                success: false,
                message: `Failed to download shared file: ${error.message}`,
            };
        }
    }
};
exports.FileService = FileService;
exports.FileService = FileService = FileService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [sui_service_1.SuiService,
        auth_service_1.AuthService,
        walrus_service_1.WalrusService,
        seal_service_1.SealService,
        access_control_service_1.AccessControlService])
], FileService);
//# sourceMappingURL=file.service.js.map