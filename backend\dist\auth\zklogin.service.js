"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ZkLoginService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZkLoginService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@mysten/sui/client");
const ed25519_1 = require("@mysten/sui/keypairs/ed25519");
const zklogin_1 = require("@mysten/sui/zklogin");
const jose = require("jose");
const zklogin_config_1 = require("../config/zklogin.config");
let ZkLoginService = ZkLoginService_1 = class ZkLoginService {
    logger = new common_1.Logger(ZkLoginService_1.name);
    config;
    suiClient;
    constructor() {
        this.config = zklogin_config_1.defaultZkLoginConfig;
        this.suiClient = new client_1.SuiClient({ url: this.config.sui.rpcUrl });
    }
    async generateEphemeralKeyPair() {
        try {
            const { epoch } = await this.suiClient.getLatestSuiSystemState();
            const maxEpoch = Number(epoch) + this.config.zkLogin.maxEpoch;
            const ephemeralKeypair = new ed25519_1.Ed25519Keypair();
            const randomness = (0, zklogin_1.generateRandomness)();
            return {
                keypair: ephemeralKeypair,
                maxEpoch,
                randomness,
            };
        }
        catch (error) {
            this.logger.error('Failed to generate ephemeral key pair', error);
            throw new Error('Failed to generate ephemeral key pair');
        }
    }
    async createZkLoginSession(provider) {
        try {
            const ephemeralKeyPair = await this.generateEphemeralKeyPair();
            const extendedEphemeralPublicKey = (0, zklogin_1.getExtendedEphemeralPublicKey)(ephemeralKeyPair.keypair.getPublicKey());
            const nonce = (0, zklogin_1.generateNonce)(ephemeralKeyPair.keypair.getPublicKey(), ephemeralKeyPair.maxEpoch, ephemeralKeyPair.randomness);
            const session = {
                ephemeralKeyPair,
                nonce,
                provider,
                maxEpoch: ephemeralKeyPair.maxEpoch,
                userSalt: this.config.zkLogin.salt,
            };
            const authUrl = this.generateOAuthUrl(provider, nonce);
            return { authUrl, session };
        }
        catch (error) {
            this.logger.error('Failed to create zkLogin session', error);
            throw new Error('Failed to create zkLogin session');
        }
    }
    generateOAuthUrl(provider, nonce) {
        const providerConfig = zklogin_config_1.oauthProviderConfigs[provider];
        const oauthConfig = this.config.oauth[provider];
        this.logger.log(`Generating OAuth URL for ${provider}`);
        this.logger.log(`Client ID: ${oauthConfig.clientId}`);
        this.logger.log(`Redirect URI: ${oauthConfig.redirectUri}`);
        const params = new URLSearchParams({
            client_id: oauthConfig.clientId,
            redirect_uri: oauthConfig.redirectUri,
            response_type: 'code',
            scope: providerConfig.scope,
            nonce,
        });
        if (provider === zklogin_config_1.OAuthProvider.GOOGLE) {
            params.append('access_type', 'offline');
        }
        else if (provider === zklogin_config_1.OAuthProvider.APPLE) {
            params.append('response_mode', 'form_post');
        }
        return `${providerConfig.authUrl}?${params.toString()}`;
    }
    async exchangeCodeForToken(provider, code, redirectUri) {
        try {
            this.logger.log(`Exchanging OAuth code for token: provider=${provider}`);
            const providerConfig = zklogin_config_1.oauthProviderConfigs[provider];
            const oauthConfig = this.config.oauth[provider];
            const headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
            };
            if (provider === zklogin_config_1.OAuthProvider.GITHUB) {
                headers['Accept'] = 'application/json';
            }
            const tokenParams = new URLSearchParams({
                client_id: oauthConfig.clientId,
                client_secret: process.env[`${provider.toUpperCase()}_CLIENT_SECRET`] || '',
                code,
                grant_type: 'authorization_code',
                redirect_uri: redirectUri || oauthConfig.redirectUri,
            });
            const response = await fetch(providerConfig.tokenUrl, {
                method: 'POST',
                headers,
                body: tokenParams.toString(),
            });
            if (!response.ok) {
                throw new Error(`OAuth token exchange failed: ${response.statusText}`);
            }
            const tokenData = await response.json();
            if (provider === zklogin_config_1.OAuthProvider.GITHUB) {
                return await this.createGitHubJWT(tokenData.access_token);
            }
            return tokenData.id_token || tokenData.access_token;
        }
        catch (error) {
            this.logger.error('Failed to exchange OAuth code for token', error);
            throw new Error('Failed to exchange OAuth code for token');
        }
    }
    async createGitHubJWT(accessToken) {
        try {
            const userResponse = await fetch('https://api.github.com/user', {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Accept': 'application/vnd.github.v3+json',
                },
            });
            if (!userResponse.ok) {
                throw new Error('Failed to fetch GitHub user info');
            }
            const userData = await userResponse.json();
            const emailResponse = await fetch('https://api.github.com/user/emails', {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Accept': 'application/vnd.github.v3+json',
                },
            });
            let email = userData.email;
            if (!email && emailResponse.ok) {
                const emails = await emailResponse.json();
                const primaryEmail = emails.find((e) => e.primary);
                email = primaryEmail?.email || emails[0]?.email;
            }
            const payload = {
                sub: userData.id.toString(),
                aud: 'github-oauth',
                iss: 'https://github.com',
                email: email || `${userData.login}@github.local`,
                name: userData.name || userData.login,
                login: userData.login,
                iat: Math.floor(Date.now() / 1000),
                exp: Math.floor(Date.now() / 1000) + 3600,
            };
            const secret = new TextEncoder().encode(this.config.jwt.secret);
            const jwt = await new jose.SignJWT(payload)
                .setProtectedHeader({ alg: 'HS256' })
                .setIssuedAt()
                .setExpirationTime('1h')
                .sign(secret);
            return jwt;
        }
        catch (error) {
            this.logger.error('Failed to create GitHub JWT', error);
            throw new Error('Failed to create GitHub JWT');
        }
    }
    async verifyJwtToken(token, provider) {
        try {
            const decoded = jose.decodeJwt(token);
            if (!decoded.sub || !decoded.aud || !decoded.iss) {
                throw new Error('Invalid JWT token: missing required fields');
            }
            const expectedIssuers = {
                [zklogin_config_1.OAuthProvider.GOOGLE]: 'https://accounts.google.com',
                [zklogin_config_1.OAuthProvider.FACEBOOK]: 'https://www.facebook.com',
                [zklogin_config_1.OAuthProvider.TWITCH]: 'https://id.twitch.tv/oauth2',
                [zklogin_config_1.OAuthProvider.APPLE]: 'https://appleid.apple.com',
                [zklogin_config_1.OAuthProvider.GITHUB]: 'https://github.com',
            };
            if (!decoded.iss.startsWith(expectedIssuers[provider])) {
                throw new Error(`Invalid issuer for ${provider}: ${decoded.iss}`);
            }
            return decoded;
        }
        catch (error) {
            this.logger.error('Failed to verify JWT token', error);
            throw new Error('Failed to verify JWT token');
        }
    }
    async generateZkLoginProof(jwt, ephemeralKeyPair, userSalt) {
        try {
            this.logger.log('Generating zkLogin proof using Mysten Labs prover service...');
            const extendedEphemeralPublicKey = (0, zklogin_1.getExtendedEphemeralPublicKey)(ephemeralKeyPair.keypair.getPublicKey());
            const requestPayload = {
                jwt,
                extendedEphemeralPublicKey: extendedEphemeralPublicKey.toString(),
                maxEpoch: ephemeralKeyPair.maxEpoch.toString(),
                jwtRandomness: ephemeralKeyPair.randomness,
                salt: userSalt,
                keyClaimName: 'sub',
            };
            this.logger.log('Calling prover service with payload:', {
                maxEpoch: requestPayload.maxEpoch,
                keyClaimName: requestPayload.keyClaimName,
            });
            const proverResponse = await fetch(this.config.zkLogin.proverUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestPayload),
            });
            if (!proverResponse.ok) {
                const errorText = await proverResponse.text();
                this.logger.error(`Prover service failed with status ${proverResponse.status}:`, errorText);
                throw new Error(`Prover service failed: ${proverResponse.status} ${proverResponse.statusText}`);
            }
            const proof = await proverResponse.json();
            this.logger.log('zkLogin proof generated successfully');
            return proof;
        }
        catch (error) {
            this.logger.error('Failed to generate zkLogin proof:', error);
            throw new Error(`Failed to generate zkLogin proof: ${error.message}`);
        }
    }
    async completeAuthentication(session, jwt) {
        try {
            this.logger.log('Verifying JWT token...');
            const decodedJwt = await this.verifyJwtToken(jwt, session.provider);
            this.logger.log('JWT verified successfully, decoded claims:', {
                sub: decodedJwt.sub,
                email: decodedJwt.email,
                name: decodedJwt.name,
                iss: decodedJwt.iss
            });
            this.logger.log('Generating zkLogin proof...');
            let zkLoginProof;
            try {
                zkLoginProof = await this.generateZkLoginProof(jwt, session.ephemeralKeyPair, session.userSalt);
                this.logger.log('zkLogin proof generated successfully');
            }
            catch (error) {
                this.logger.warn('Failed to generate zkLogin proof, falling back to development mode:', error.message);
            }
            const zkLoginAddress = await this.deriveZkLoginAddressFromJWT(jwt, session.userSalt);
            this.logger.log(`Generated zkLogin address: ${zkLoginAddress}`);
            return {
                zkLoginAddress,
                provider: session.provider,
                email: decodedJwt.email,
                name: decodedJwt.name,
                sub: decodedJwt.sub,
                aud: decodedJwt.aud,
                iss: decodedJwt.iss,
                ephemeralKeyPair: session.ephemeralKeyPair,
                zkLoginProof,
                jwt,
                userSalt: session.userSalt,
            };
        }
        catch (error) {
            this.logger.error('Failed to complete authentication', error);
            this.logger.error('Error details:', error.stack);
            throw new Error('Failed to complete authentication');
        }
    }
    async deriveZkLoginAddressFromJWT(jwt, salt) {
        try {
            const { jwtToAddress } = await Promise.resolve().then(() => require('@mysten/sui/zklogin'));
            const zkLoginAddress = jwtToAddress(jwt, salt);
            this.logger.log(`Derived zkLogin address using official algorithm: ${zkLoginAddress}`);
            return zkLoginAddress;
        }
        catch (error) {
            this.logger.warn('Failed to use official zkLogin address derivation, falling back to simplified version:', error.message);
            return this.deriveZkLoginAddressSimplified(jwt, salt);
        }
    }
    deriveZkLoginAddressSimplified(jwt, salt) {
        const crypto = require('crypto');
        const decoded = JSON.parse(Buffer.from(jwt.split('.')[1], 'base64').toString());
        const addressSeed = `${decoded.sub}_${decoded.iss}_${salt}`;
        const hash = crypto.createHash('sha256').update(addressSeed).digest('hex');
        const suiAddress = `0x${hash.slice(0, 64)}`;
        this.logger.log(`Derived address from seed (simplified): ${addressSeed} -> ${suiAddress}`);
        return suiAddress;
    }
    deriveZkLoginAddress(decodedJwt, salt) {
        const crypto = require('crypto');
        const addressSeed = `${decodedJwt.sub}_${decodedJwt.iss}_${salt}`;
        const hash = crypto.createHash('sha256').update(addressSeed).digest('hex');
        const suiAddress = `0x${hash.slice(0, 64)}`;
        this.logger.log(`Derived address from seed: ${addressSeed} -> ${suiAddress}`);
        return suiAddress;
    }
};
exports.ZkLoginService = ZkLoginService;
exports.ZkLoginService = ZkLoginService = ZkLoginService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], ZkLoginService);
//# sourceMappingURL=zklogin.service.js.map