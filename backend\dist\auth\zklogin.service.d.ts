import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';
import { OAuthProvider } from '../config/zklogin.config';
export interface EphemeralKeyPair {
    keypair: Ed25519Keypair;
    maxEpoch: number;
    randomness: string;
}
export interface ZkLoginSession {
    ephemeralKeyPair: EphemeralKeyPair;
    nonce: string;
    provider: OAuthProvider;
    maxEpoch: number;
    userSalt: string;
}
export interface ZkLoginProof {
    proofPoints: {
        a: string[];
        b: string[][];
        c: string[];
    };
    issBase64Details: {
        value: string;
        indexMod4: number;
    };
    headerBase64: string;
}
export interface AuthenticatedUser {
    zkLoginAddress: string;
    provider: OAuthProvider;
    email?: string;
    name?: string;
    sub: string;
    aud: string;
    iss: string;
    ephemeralKeyPair?: EphemeralKeyPair;
    zkLoginProof?: ZkLoginProof;
    jwt?: string;
    userSalt?: string;
}
export declare class ZkLoginService {
    private readonly logger;
    private readonly config;
    private readonly suiClient;
    constructor();
    generateEphemeralKeyPair(): Promise<EphemeralKeyPair>;
    createZkLoginSession(provider: OAuthProvider): Promise<{
        authUrl: string;
        session: ZkLoginSession;
    }>;
    private generateOAuthUrl;
    exchangeCodeForToken(provider: OAuthProvider, code: string, redirectUri?: string): Promise<string>;
    private createGitHubJWT;
    verifyJwtToken(token: string, provider: OAuthProvider): Promise<any>;
    generateZkLoginProof(jwt: string, ephemeralKeyPair: EphemeralKeyPair, userSalt: string): Promise<ZkLoginProof>;
    completeAuthentication(session: ZkLoginSession, jwt: string): Promise<AuthenticatedUser>;
    private deriveZkLoginAddressFromJWT;
    private deriveZkLoginAddressSimplified;
    private deriveZkLoginAddress;
}
