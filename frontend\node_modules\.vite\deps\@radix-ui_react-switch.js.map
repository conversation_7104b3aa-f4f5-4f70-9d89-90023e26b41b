{"version": 3, "sources": ["../../@radix-ui/react-switch/src/Switch.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Switch\n * -----------------------------------------------------------------------------------------------*/\n\nconst SWITCH_NAME = 'Switch';\n\ntype ScopedProps<P> = P & { __scopeSwitch?: Scope };\nconst [createSwitchContext, createSwitchScope] = createContextScope(SWITCH_NAME);\n\ntype SwitchContextValue = { checked: boolean; disabled?: boolean };\nconst [SwitchProvider, useSwitchContext] = createSwitchContext<SwitchContextValue>(SWITCH_NAME);\n\ntype SwitchElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SwitchProps extends PrimitiveButtonProps {\n  checked?: boolean;\n  defaultChecked?: boolean;\n  required?: boolean;\n  onCheckedChange?(checked: boolean): void;\n}\n\nconst Switch = React.forwardRef<SwitchElement, SwitchProps>(\n  (props: ScopedProps<SwitchProps>, forwardedRef) => {\n    const {\n      __scopeSwitch,\n      name,\n      checked: checkedProp,\n      defaultChecked,\n      required,\n      disabled,\n      value = 'on',\n      onCheckedChange,\n      form,\n      ...switchProps\n    } = props;\n    const [button, setButton] = React.useState<HTMLButtonElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = button ? form || !!button.closest('form') : true;\n    const [checked = false, setChecked] = useControllableState({\n      prop: checkedProp,\n      defaultProp: defaultChecked,\n      onChange: onCheckedChange,\n    });\n\n    return (\n      <SwitchProvider scope={__scopeSwitch} checked={checked} disabled={disabled}>\n        <Primitive.button\n          type=\"button\"\n          role=\"switch\"\n          aria-checked={checked}\n          aria-required={required}\n          data-state={getState(checked)}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          value={value}\n          {...switchProps}\n          ref={composedRefs}\n          onClick={composeEventHandlers(props.onClick, (event) => {\n            setChecked((prevChecked) => !prevChecked);\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              // if switch is in a form, stop propagation from the button so that we only propagate\n              // one click event (from the input). We propagate changes from an input so that native\n              // form validation works and form events reflect switch updates.\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })}\n        />\n        {isFormControl && (\n          <BubbleInput\n            control={button}\n            bubbles={!hasConsumerStoppedPropagationRef.current}\n            name={name}\n            value={value}\n            checked={checked}\n            required={required}\n            disabled={disabled}\n            form={form}\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            style={{ transform: 'translateX(-100%)' }}\n          />\n        )}\n      </SwitchProvider>\n    );\n  }\n);\n\nSwitch.displayName = SWITCH_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SwitchThumb\n * -----------------------------------------------------------------------------------------------*/\n\nconst THUMB_NAME = 'SwitchThumb';\n\ntype SwitchThumbElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SwitchThumbProps extends PrimitiveSpanProps {}\n\nconst SwitchThumb = React.forwardRef<SwitchThumbElement, SwitchThumbProps>(\n  (props: ScopedProps<SwitchThumbProps>, forwardedRef) => {\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return (\n      <Primitive.span\n        data-state={getState(context.checked)}\n        data-disabled={context.disabled ? '' : undefined}\n        {...thumbProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nSwitchThumb.displayName = THUMB_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype InputProps = React.ComponentPropsWithoutRef<'input'>;\ninterface BubbleInputProps extends Omit<InputProps, 'checked'> {\n  checked: boolean;\n  control: HTMLElement | null;\n  bubbles: boolean;\n}\n\nconst BubbleInput = (props: BubbleInputProps) => {\n  const { control, checked, bubbles = true, ...inputProps } = props;\n  const ref = React.useRef<HTMLInputElement>(null);\n  const prevChecked = usePrevious(checked);\n  const controlSize = useSize(control);\n\n  // Bubble checked change to parents (e.g form change event)\n  React.useEffect(() => {\n    const input = ref.current!;\n    const inputProto = window.HTMLInputElement.prototype;\n    const descriptor = Object.getOwnPropertyDescriptor(inputProto, 'checked') as PropertyDescriptor;\n    const setChecked = descriptor.set;\n    if (prevChecked !== checked && setChecked) {\n      const event = new Event('click', { bubbles });\n      setChecked.call(input, checked);\n      input.dispatchEvent(event);\n    }\n  }, [prevChecked, checked, bubbles]);\n\n  return (\n    <input\n      type=\"checkbox\"\n      aria-hidden\n      defaultChecked={checked}\n      {...inputProps}\n      tabIndex={-1}\n      ref={ref}\n      style={{\n        ...props.style,\n        ...controlSize,\n        position: 'absolute',\n        pointerEvents: 'none',\n        opacity: 0,\n        margin: 0,\n      }}\n    />\n  );\n};\n\nfunction getState(checked: boolean) {\n  return checked ? 'checked' : 'unchecked';\n}\n\nconst Root = Switch;\nconst Thumb = SwitchThumb;\n\nexport {\n  createSwitchScope,\n  //\n  Switch,\n  SwitchThumb,\n  //\n  Root,\n  Thumb,\n};\nexport type { SwitchProps, SwitchThumbProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AA0DjB,yBAAA;AA3CN,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,WAAW;AAG/E,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,WAAW;AAW9F,IAAM,SAAe;EACnB,CAAC,OAAiC,iBAAiB;AACjD,UAAM;MACJ;MACA;MACA,SAAS;MACT;MACA;MACA;MACA,QAAQ;MACR;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,CAAC,QAAQ,SAAS,IAAU,eAAmC,IAAI;AACzE,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,UAAU,IAAI,CAAC;AAC5E,UAAM,mCAAyC,aAAO,KAAK;AAE3D,UAAM,gBAAgB,SAAS,QAAQ,CAAC,CAAC,OAAO,QAAQ,MAAM,IAAI;AAClE,UAAM,CAAC,UAAU,OAAO,UAAU,IAAI,qBAAqB;MACzD,MAAM;MACN,aAAa;MACb,UAAU;IACZ,CAAC;AAED,eACE,yBAAC,gBAAA,EAAe,OAAO,eAAe,SAAkB,UACtD,UAAA;UAAA;QAAC,UAAU;QAAV;UACC,MAAK;UACL,MAAK;UACL,gBAAc;UACd,iBAAe;UACf,cAAY,SAAS,OAAO;UAC5B,iBAAe,WAAW,KAAK;UAC/B;UACA;UACC,GAAG;UACJ,KAAK;UACL,SAAS,qBAAqB,MAAM,SAAS,CAAC,UAAU;AACtD,uBAAW,CAAC,gBAAgB,CAAC,WAAW;AACxC,gBAAI,eAAe;AACjB,+CAAiC,UAAU,MAAM,qBAAqB;AAItE,kBAAI,CAAC,iCAAiC,QAAS,OAAM,gBAAgB;YACvE;UACF,CAAC;QAAA;MACH;MACC,qBACC;QAAC;QAAA;UACC,SAAS;UACT,SAAS,CAAC,iCAAiC;UAC3C;UACA;UACA;UACA;UACA;UACA;UAIA,OAAO,EAAE,WAAW,oBAAoB;QAAA;MAC1C;IAAA,EAAA,CAEJ;EAEJ;AACF;AAEA,OAAO,cAAc;AAMrB,IAAM,aAAa;AAMnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,eACE;MAAC,UAAU;MAAV;QACC,cAAY,SAAS,QAAQ,OAAO;QACpC,iBAAe,QAAQ,WAAW,KAAK;QACtC,GAAG;QACJ,KAAK;MAAA;IACP;EAEJ;AACF;AAEA,YAAY,cAAc;AAW1B,IAAM,cAAc,CAAC,UAA4B;AAC/C,QAAM,EAAE,SAAS,SAAS,UAAU,MAAM,GAAG,WAAW,IAAI;AAC5D,QAAM,MAAY,aAAyB,IAAI;AAC/C,QAAM,cAAc,YAAY,OAAO;AACvC,QAAM,cAAc,QAAQ,OAAO;AAG7B,EAAA,gBAAU,MAAM;AACpB,UAAM,QAAQ,IAAI;AAClB,UAAM,aAAa,OAAO,iBAAiB;AAC3C,UAAM,aAAa,OAAO,yBAAyB,YAAY,SAAS;AACxE,UAAM,aAAa,WAAW;AAC9B,QAAI,gBAAgB,WAAW,YAAY;AACzC,YAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC;AAC5C,iBAAW,KAAK,OAAO,OAAO;AAC9B,YAAM,cAAc,KAAK;IAC3B;EACF,GAAG,CAAC,aAAa,SAAS,OAAO,CAAC;AAElC,aACE;IAAC;IAAA;MACC,MAAK;MACL,eAAW;MACX,gBAAgB;MACf,GAAG;MACJ,UAAU;MACV;MACA,OAAO;QACL,GAAG,MAAM;QACT,GAAG;QACH,UAAU;QACV,eAAe;QACf,SAAS;QACT,QAAQ;MACV;IAAA;EACF;AAEJ;AAEA,SAAS,SAAS,SAAkB;AAClC,SAAO,UAAU,YAAY;AAC/B;AAEA,IAAM,OAAO;AACb,IAAM,QAAQ;", "names": []}